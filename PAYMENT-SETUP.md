# eSIMConnect Payment Integration

This document provides an overview of the payment integration for the eSIMConnect platform, a Laravel-based eSIM marketplace that allows travelers to purchase and manage international eSIM data plans.

## Overview

eSIMConnect uses Stripe for payment processing. The integration includes:

- Secure checkout process
- Payment intent creation
- Credit card processing
- Webhook handling for asynchronous payment events
- Order management
- Payment status tracking

## Setup Instructions

### 1. Stripe Account Setup

1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Obtain your API keys from the Stripe Dashboard:
   - Publishable Key (starts with `pk_`)
   - Secret Key (starts with `sk_`)
   - Webhook Secret (starts with `whsec_`)

### 2. Environment Configuration

Update your `.env` file with the following Stripe-related variables:

```
STRIPE_KEY=pk_test_your_publishable_key
STRIPE_SECRET=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### 3. Webhook Configuration

1. In your Stripe Dashboard, go to Developers > Webhooks
2. Add a new endpoint with the URL: `https://your-domain.com/webhook/stripe`
3. Select the following events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `charge.refunded`

### 4. Testing the Integration

1. Use Stripe's test cards to simulate payments:
   - Success: `4242 4242 4242 4242`
   - Requires Authentication: `4000 0025 0000 3155`
   - Declined: `4000 0000 0000 0002`
2. Enter any future expiration date and any 3-digit CVC

## Implementation Details

### Key Components

1. **StripeService**: Service class that handles Stripe API interactions
   - Located at: `app/Services/StripeService.php`
   - Methods for creating payment intents, retrieving payment details, and processing payments

2. **PaymentController**: Controller for handling payment-related requests
   - Located at: `app/Http/Controllers/PaymentController.php`
   - Manages checkout flow, payment intent creation, and payment processing

3. **WebhookController**: Controller for handling Stripe webhook events
   - Located at: `app/Http/Controllers/WebhookController.php`
   - Processes asynchronous payment events from Stripe

4. **Checkout Views**: Frontend templates for the checkout process
   - Located at: `resources/views/payments/`
   - Includes checkout, success, and cancel pages

5. **JavaScript Utilities**: Client-side scripts for payment processing
   - Located at: `public/js/stripe-checkout.js` and `public/js/payment-helpers.js`
   - Handles Stripe Elements integration and payment form submission

### Payment Flow

1. User adds eSIMs to cart
2. User proceeds to checkout
3. System creates a payment intent via Stripe
4. User enters payment details
5. Payment is processed through Stripe
6. On successful payment:
   - Orders are created in the database
   - User is redirected to success page
   - eSIM details are provided for activation
7. On failed payment:
   - User is shown error messages
   - User can retry the payment

### Webhook Handling

Webhooks ensure that orders are properly updated even if the user closes their browser during the payment process. The system listens for the following events:

- `payment_intent.succeeded`: Marks orders as paid and generates eSIM details
- `payment_intent.payment_failed`: Marks orders as failed
- `charge.refunded`: Marks orders as refunded

## Security Considerations

1. All sensitive payment data is handled directly by Stripe
2. No credit card information is stored on our servers
3. All API requests use HTTPS
4. Webhook signatures are verified to prevent tampering
5. CSRF protection is enabled for all form submissions

## Troubleshooting

### Common Issues

1. **Webhook not receiving events**
   - Verify webhook URL is correct
   - Check webhook secret in `.env` file
   - Ensure server is publicly accessible

2. **Payment failing**
   - Check Stripe Dashboard for error messages
   - Verify API keys are correct
   - Test with Stripe's test cards

3. **Order not updating after payment**
   - Check webhook logs
   - Verify webhook is properly configured
   - Check for errors in `storage/logs/laravel.log`

## Additional Resources

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Laravel Stripe Integration Guide](https://laravel.com/docs/10.x/billing)
- [Stripe Elements Documentation](https://stripe.com/docs/elements)
