<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Esim;

class EsimController extends Controller
{
    public function index()
    {
        $esims = Esim::all();
        return view('admin.esims.index', compact('esims'));
    }

    public function create()
    {
        return view('admin.esims.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'country_id' => 'required|exists:countries,id',
        ]);
        Esim::create($validated);
        return redirect()->route('admin.esims.index')->with('success', 'eSIM created successfully.');
    }

    public function show(Esim $esim)
    {
        return view('admin.esims.show', compact('esim'));
    }

    public function edit(Esim $esim)
    {
        return view('admin.esims.edit', compact('esim'));
    }

    public function update(Request $request, Esim $esim)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'country_id' => 'required|exists:countries,id',
        ]);
        $esim->update($validated);
        return redirect()->route('admin.esims.index')->with('success', 'eSIM updated successfully.');
    }

    public function destroy(Esim $esim)
    {
        $esim->delete();
        return redirect()->route('admin.esims.index')->with('success', 'eSIM deleted successfully.');
    }
}
