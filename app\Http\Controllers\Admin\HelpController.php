<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Help;

class HelpController extends Controller
{
    public function index()
    {
        $helps = Help::all();
        return view('admin.help.index', compact('helps'));
    }

    public function create()
    {
        return view('admin.help.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
        ]);
        Help::create($validated);
        return redirect()->route('admin.help.index')->with('success', 'Help article created successfully.');
    }

    public function show(Help $help)
    {
        return view('admin.help.show', compact('help'));
    }

    public function edit(Help $help)
    {
        return view('admin.help.edit', compact('help'));
    }

    public function update(Request $request, Help $help)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
        ]);
        $help->update($validated);
        return redirect()->route('admin.help.index')->with('success', 'Help article updated successfully.');
    }

    public function destroy(Help $help)
    {
        $help->delete();
        return redirect()->route('admin.help.index')->with('success', 'Help article deleted successfully.');
    }
}
