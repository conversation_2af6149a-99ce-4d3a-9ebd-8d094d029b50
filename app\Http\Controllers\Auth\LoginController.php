<?php

namespace App\Http\Controllers\Auth;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Notifications\LoginLink;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Auth\RedirectResponse;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = 'dashboard';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }
    /**
     * Login page
     *
     * <AUTHOR> <https://gempixel.com> 
     * @version 1.0
     * @return void
     */
    public function login(){
        return view('auth.login');
    }
    /**
     * Process Login
     *
     * <AUTHOR> <https://gempixel.com> 
     * @version 1.0
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    public function process(Request $request){
        $validatedData = $request->validate([
            'email' => 'required|email',
        ]);

        $user = User::where('email', $validatedData['email'])->first();

        if (!$user) {
            return back()->withError('The email does not exist.');
        }

        $token = Str::random(60);
        $user->remember_token = $token;
        $user->save();

        $user->notify(new LoginLink($token));

        return back()->with('success', 'A login link has been sent to your email.');
    }

    /**
     * Verify login token and authenticate user
     *
     * <AUTHOR> <https://gempixel.com> 
     * @version 1.0
     * @param string $token
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verify($token)
    {
        $user = User::where('remember_token', $token)->first();

        if (!$user) {
            return redirect()->route('login')->withError('Invalid or expired login link.');
        }

        // Clear the token after successful verification
        $user->remember_token = null;
        $user->save();

        // Log the user in
        auth()->login($user);

        return redirect()->intended($this->redirectTo);
    }
    /**
     * Logout
     *
     * <AUTHOR> <https://gempixel.com> 
     * @version 1.0
     * @param \Illuminate\Http\Request $request
     * @return RedirectResponse
     */
    public function logout(Request $request){
        Auth::logout();
     
        $request->session()->invalidate();
     
        $request->session()->regenerateToken();
     
        return redirect()->route('home');
    }    
}