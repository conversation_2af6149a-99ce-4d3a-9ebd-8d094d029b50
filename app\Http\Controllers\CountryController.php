<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Country;
use App\Models\Esim;
use App\Services\AiraloService;
use Illuminate\Support\Facades\Log;

class CountryController extends Controller
{
    protected $airaloService;

    public function __construct(AiraloService $airaloService)
    {
        $this->airaloService = $airaloService;
    }

    /**
     * Display a listing of all countries.
     */
    public function index()
    {
        $countries = Country::orderBy('name')->get();
        
        return view('countries.index', [
            'countries' => $countries,
        ]);
    }

    /**
     * Display the specified country.
     */
    public function show($code)
    {
        $country = Country::where('slug', $code)->firstOrFail();
        $esims = Esim::where('country_id', $country->id)->orderBy('price')->get();
        
        return view('countries.show', [
            'country' => $country,
            'esims' => $esims,
        ]);
    }

    /**
     * Sync countries from Airalo API to database.
     */
    public function syncFromApi()
    {
        try {
            $regions = $this->airaloService->getCountries();

            $count = 0;
            

            foreach($regions as $region){
                
                $country = Country::updateOrCreate([
                    'slug' => $region->slug,
                ],
                [
                        'name' => $region->title,
                        'slug' => $region->slug,
                        'code' => $region->slug,
                        'flag_url' => $region->image->url,
                        'description' => null,
                        'is_popular' => 0,
                        'is_region' => 1,
                        'is_global' => 0                        
                ]);                

                foreach ($region->operators as $operator) {
                    foreach ($operator->packages as $esimData) {
                        if($esimData['type'] !== 'sim') continue;
                        $esim = Esim::updateOrCreate(
                            ['product_id' => $esimData['id']],
                            [
                                'country_id' => $country->id,
                                'name' => $esimData['title'],
                                'operator' => $operator->title,
                                'description' => json_encode($operator->info),
                                'data_amount' => str_replace('GB', '', $esimData['data']) ?? null,
                                'validity_days' => $esimData['day'] ?? null,
                                'price' => $esimData['price'] ?? null,
                                'currency' => 'USD',
                                'is_popular' => false,
                                'features' => json_encode($esimData['features'] ?? []),
                                'coverage' => $esimData['coverage'] ?? null,
                            ]
                        );
                    }
                }
            }
            // exit;
            // foreach (Country::all() as $country) {
                
            //     // Sync eSIMs for this country
            //     $this->syncEsimsForCountry($country);
                
            //     $count++;
            // }
            
            return redirect()->route('countries.index')->with('success', "Successfully synced {$count} countries from Airalo API.");
        } catch (\Exception $e) {
            Log::error('Error syncing countries from Airalo API', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('countries.index')->with('error', 'Error syncing countries: ' . $e->getMessage());
        }
    }
    
    /**
     * Sync eSIMs for a specific country
     * 
     * @param Country $country
     * @return void
     */
    protected function syncEsimsForCountry(Country $country)
    {
        try {
            $esims = $this->airaloService->getEsimsByCountry(strtoupper($country->code));
            if($country->id == 202) return;
            foreach ($esims as $esim) {
                foreach ($esim->operators as $operator) {
                    foreach ($operator->packages as $esimData) {
                        if($esimData['type'] !== 'sim') continue;
                        $esim = Esim::updateOrCreate(
                            ['product_id' => $esimData['id']],
                            [
                                'country_id' => $country->id,
                                'name' => $esimData['title'],
                                'operator' => $operator->title,
                                'description' => json_encode($operator->info),
                                'data_amount' => str_replace('GB', '', $esimData['data']) ?? null,
                                'validity_days' => $esimData['day'] ?? null,
                                'price' => $esimData['price'] ?? null,
                                'currency' => 'USD',
                                'is_popular' => false,
                                'features' => json_encode($esimData['features'] ?? []),
                                'coverage' => $esimData['coverage'] ?? null,
                            ]
                        );
                    }
                }
            }
            
            Log::info("Synced " . count($esims) . " eSIMs for {$country->name}");
        } catch (\Exception $e) {
            Log::error("Error syncing eSIMs for country {$country->name}", [
                'error' => $e->getMessage(),
                'country_code' => $country->code
            ]);
        }
    }

    /**
     * Redirect to country by 2-letter code via /packages/{package?}
     * <AUTHOR>
     */
    public function redirectToCountryByCode()
    {
        $package = request()->package;
        
        if (empty($package)) {
            return redirect('/countries', 301);
        }
        $country = Country::where('code', strtoupper($package))->first();
        if ($country) {
            return redirect()->route('countries.show', ['code' => $country->slug], 301);
        }
        return redirect('/countries', 301);
    }
}
