<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Coupon;
use Illuminate\Support\Facades\Session;

class CouponController extends Controller
{
    public function apply(Request $request)
    {
        $code = $request->input('code');
        $coupon = Coupon::validateAndGet($code);
        if ($coupon) {
            Session::put('coupon', [
                'code' => $coupon->code,
                'discount_percent' => $coupon->discount_percent
            ]);
            return response()->json(['success' => true, 'discount_percent' => $coupon->discount_percent]);
        } else {
            Session::forget('coupon');
            return response()->json(['success' => false, 'message' => 'Invalid or expired coupon code.']);
        }
    }

    public function remove()
    {
        Session::forget('coupon');
        return response()->json(['success' => true]);
    }
}
