<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Order;
use App\Models\Esim;
use Stripe\Stripe;
use Stripe\Customer;
use Stripe\PaymentMethod;
use Stripe\SetupIntent;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the user dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get recent orders
        $recentOrders = $user->orders()
            ->with(['esim', 'esim.country'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
        
        // Get active eSIMs (orders with paid status)
        $activeEsims = $user->orders()
            ->with(['esim', 'esim.country'])
            ->where('payment_status', 'paid')
            ->orderBy('created_at', 'desc')
            ->get();
        
        // Calculate total spent
        $totalSpent = $user->orders()
            ->where('payment_status', 'paid')
            ->sum('amount');
        
        // Get order statistics
        $orderStats = [
            'total' => $user->orders()->count(),
            'completed' => $user->orders()->where('payment_status', 'paid')->count(),
            'pending' => $user->orders()->where('payment_status', 'pending')->count(),
            'failed' => $user->orders()->where('payment_status', 'failed')->count()
        ];
        
        return view('dashboard.index', [
            'user' => $user,
            'recentOrders' => $recentOrders,
            'activeEsims' => $activeEsims,
            'totalSpent' => $totalSpent,
            'orderStats' => $orderStats
        ]);
    }

    /**
     * Show the user's active eSIMs.
     *
     * @return \Illuminate\View\View
     */
    public function esims()
    {
        $user = Auth::user();
        
        // Get active eSIMs (orders with paid status)
        $activeEsims = $user->orders()
            ->with(['esim', 'esim.country'])
            ->where('payment_status', 'paid')
            ->orderBy('created_at', 'desc')
            ->get();
        
        return view('dashboard.esims', [
            'user' => $user,
            'activeEsims' => $activeEsims
        ]);
    }

    /**
     * Show the user's payment methods.
     *
     * @return \Illuminate\View\View
     */
    public function paymentMethods()
    {
        $user = Auth::user();
        $stripeKey = config('services.stripe.secret');
        Stripe::setApiKey($stripeKey);

        // Ensure user has a Stripe customer id
        if (!$user->stripe_customer_id) {
            $customer = Customer::create([
                'email' => $user->email,
                'name' => $user->name,
            ]);
            $user->stripe_customer_id = $customer->id;
            $user->save();
        }

        // Fetch payment methods
        $paymentMethods = PaymentMethod::all([
            'customer' => $user->stripe_customer_id,
            'type' => 'card',
        ]);

        // SetupIntent for Stripe Elements
        $setupIntent = SetupIntent::create([
            'customer' => $user->stripe_customer_id,
        ]);

        // Fetch billing history (orders)
        $orders = $user->orders()->orderBy('created_at', 'desc')->get();

        return view('dashboard.payment-methods', [
            'user' => $user,
            'paymentMethods' => $paymentMethods,
            'setupIntent' => $setupIntent,
            'orders' => $orders,
        ]);
    }

    /**
     * Store a new payment method for the user (AJAX endpoint)
     */
    public function addPaymentMethod(Request $request)
    {
        $user = Auth::user();
        $stripeKey = config('services.stripe.secret');
        Stripe::setApiKey($stripeKey);

        $paymentMethodId = $request->input('payment_method_id');
        if (!$paymentMethodId) {
            return response()->json(['error' => 'No payment method provided.'], 400);
        }

        // Attach payment method to customer
        $paymentMethod = PaymentMethod::retrieve($paymentMethodId);
        $paymentMethod->attach(['customer' => $user->stripe_customer_id]);

        // Optionally set as default
        $customer = Customer::update($user->stripe_customer_id, [
            'invoice_settings' => [
                'default_payment_method' => $paymentMethodId,
            ],
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Show the user's account settings.
     *
     * @return \Illuminate\View\View
     */
    public function settings()
    {
        $user = Auth::user();
        
        return view('dashboard.settings', [
            'user' => $user
        ]);
    }

    /**
     * Update the user's account settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSettings(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'current_password' => 'nullable|required_with:password|password',
            'password' => 'nullable|string|min:8|confirmed',
        ]);
        
        $user->name = $request->name;
        $user->email = $request->email;
        
        if ($request->filled('password')) {
            $user->password = bcrypt($request->password);
        }
        
        $user->save();
        
        return redirect()->route('dashboard.settings')->with('success', 'Your account settings have been updated.');
    }
}
