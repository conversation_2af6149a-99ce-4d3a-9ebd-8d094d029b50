<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Country;
use App\Models\Esim;
use App\Services\AiraloService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Session;

class EsimController extends Controller
{
    protected $airaloService;

    public function __construct(AiraloService $airaloService)
    {
        $this->airaloService = $airaloService;
    }

    /**
     * Display the specified eSIM.
     */
    public function show($id)
    {
        $esim = Esim::findOrFail($id);
        $country = $esim->country;
        
        return view('esims.show', [
            'esim' => $esim,
            'country' => $country,
        ]);
    }

    /**
     * Sync eSIMs for a specific country from Airalo API to database.
     */
    public function syncFromApi($countryCode)
    {
        try {
            $country = Country::where('code', $countryCode)->firstOrFail();
            $esimData = $this->airaloService->getEsimsByCountry($countryCode);
            $count = 0;
            
            if (empty($esimData)) {
                return redirect()->route('countries.show', $countryCode)
                    ->with('error', 'No eSIMs found for ' . $country->name . ' from the Airalo API.');
            }
            
            foreach ($esimData as $data) {
                $esim = Esim::updateOrCreate(
                    ['product_id' => $data['id']],
                    [
                        'country_id' => $country->id,
                        'name' => $data['name'],
                        'description' => $data['description'] ?? null,
                        'price' => $data['price'] ?? 0,
                        'currency' => $data['currency'] ?? 'USD',
                        'data_amount' => $data['data_amount'] ?? 'Unknown',
                        'validity_days' => $data['validity_days'] ?? 0,
                        'is_popular' => $data['is_popular'] ?? false,
                        'features' => json_encode($data['features'] ?? []),
                        'coverage' => $data['coverage'] ?? null,
                    ]
                );
                
                $count++;
            }
            
            Log::info("Synced {$count} eSIMs for {$country->name}");
            
            return redirect()->route('countries.show', $countryCode)
                ->with('success', "Synced {$count} eSIMs for {$country->name} from Airalo API.");
        } catch (\Exception $e) {
            Log::error("Error syncing eSIMs for country {$countryCode}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('countries.index')
                ->with('error', 'Error syncing eSIMs: ' . $e->getMessage());
        }
    }

    /**
     * Add an eSIM to the shopping cart.
     */
    public function addToCart(Request $request, $id)
    {
        session()->forget('cart');

        $esim = Esim::findOrFail($id);

        // Get the current cart from the session or create a new one
        $cart = session()->get('cart', []);
        
        // Add the eSIM to the cart
        $cart[$id] = [
            'id' => $esim->id,
            'name' => $esim->name,
            'price' => $esim->price(),
            'country' => $esim->country->name,
            'data_amount' => $esim->data_amount,
            'validity_days' => $esim->validity_days,
        ];
        
        // Update the cart in the session
        session()->put('cart', $cart);
        
        return redirect()->to(route('checkout'))->with('success', 'eSIM added to cart successfully!');
    }

    /**
     * Display the shopping cart.
     */
    public function cart()
    {
        $cart = session('cart', []);
        $total = 0;
        foreach ($cart as $item) {
            $total += $item['price'];
        }
        $tax = 0; // You may want to calculate tax here
        $discount = 0;
        $coupon = session('coupon');
        if ($coupon && isset($coupon['discount_percent'])) {
            $discount = ($total * $coupon['discount_percent']) / 100;
        }
        $grandTotal = $total - $discount + $tax;

        return redirect()->route('checkout');
        
        return view('esims.cart', compact('cart', 'total', 'tax', 'discount', 'grandTotal', 'coupon'));
    }

    /**
     * Remove an eSIM from the shopping cart.
     */
    public function removeFromCart($id)
    {
        $cart = session()->get('cart', []);
                                            
        if (isset($cart[$id])) {
            unset($cart[$id]);
            session()->put('cart', $cart);
        }
        
        return redirect()->back()->with('success', 'eSIM removed from cart successfully!');
    }
}
