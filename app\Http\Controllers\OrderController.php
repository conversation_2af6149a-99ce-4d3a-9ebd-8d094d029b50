<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Esim;
use App\Models\User;
use App\Services\AiraloService;
use App\Services\StripeService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class OrderController extends Controller
{
    protected $airaloService;

    public function __construct(AiraloService $airaloService)
    {
        $this->airaloService = $airaloService;
    }

    /**
     * Display a listing of the user's orders.
     */
    public function index()
    {
        $orders = Auth::user()->orders()->orderBy('created_at', 'desc')->get();
        
        return view('orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Display the specified order.
     */
    public function show($orderNumber)
    {
        $order = Order::where('order_number', $orderNumber)
            ->where('user_id', Auth::id())
            ->firstOrFail();
            
        return view('orders.show', [
            'order' => $order,
        ]);
    }

    /**
     * Show the checkout page.
     */
    public function checkout()
    {
        $cart = Session::get('cart', []);
        
        if (empty($cart)) {
            return redirect()->route('home')->with('error', 'Your cart is empty.');
        }
        
        // Redirect to the new payment checkout page
        return redirect()->route('payments.checkout');
    }

    /**
     * Process the order.
     */
    public function process(Request $request)
    {
        // This method is now handled by PaymentController
        // Redirect to the new payment checkout page
        return redirect()->route('payments.checkout');
    }

    /**
     * Display the order confirmation page.
     */
    public function confirmation($orderNumber)
    {
        $order = Order::where('order_number', $orderNumber)
            ->firstOrFail();
        
            $user = User::where('id', $order->user_id)->first();

        return view('orders.confirmation', [
            'order' => $order,
            'user' => $user
        ]);
    }
}
