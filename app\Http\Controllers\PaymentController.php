<?php

namespace App\Http\Controllers;

use App\Models\Esim;
use App\Models\Order;
use App\Services\StripeService;
use App\Services\AiraloService;
use \App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Exception;

class PaymentController extends Controller
{
    protected $stripeService;

    /**
     * Create a new controller instance.
     *
     * @param StripeService $stripeService
     */
    public function __construct(StripeService $stripeService, AiraloService $airaloService)
    {        
        $this->stripeService = $stripeService;
        $this->airaloService = $airaloService;
    }


    /**
     * Show the checkout page
     *
     * @return \Illuminate\View\View
     */
    public function checkout()
    {
        $cartItems = Session::get('cart', []);
        $total = 0;
        foreach ($cartItems as $item) {
            $total += $item['price'];
        }
        $tax = 0;
        $discount = 0;
        $coupon = Session::get('coupon');
        if ($coupon && isset($coupon['discount_percent'])) {
            $discount = ($total * $coupon['discount_percent']) / 100;
        }
        $grandTotal = $total - $discount + $tax;
        return view('payments.checkout', compact('cartItems', 'total', 'tax', 'discount', 'grandTotal', 'coupon'));
    }

    /**
     * Create a payment intent
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPaymentIntent(Request $request)
    {
        try {
            // Get cart items from session
            $cartItems = Session::get('cart', []);
            $coupon = Session::get('coupon', null);

            if (empty($cartItems)) {
                return response()->json(['error' => 'Your cart is empty.'], 400);
            }

            $total = 0;
            $esimIds = [];
            foreach ($cartItems as $id => $item) {
                // Support both array of IDs and array of item arrays
                $esim = is_array($item) ? (object)$item : \App\Models\Esim::find($id);
                if ($esim && $esim->price) {
                    $total += $esim->price;
                    $esimIds[] = $esim->id ?? $id;
                }
            }

            // Calculate discount
            $discount = 0;
            if ($coupon && isset($coupon['discount_percent'])) {
                $discount = ($total * $coupon['discount_percent']) / 100;
            }
            // Calculate tax (if any, adjust as needed)
            $tax = 0;
            // $tax = $total * 0.05; // Example: 5% tax

            $grandTotal = round($total - $discount + $tax, 2);
            if ($grandTotal < 0) $grandTotal = 0;

            // Get email from request or session (fallback to authenticated user)
            $email = $request->input('email');
            if (!$email && Auth::check()) {
                $email = Auth::user()->email;
            }

            // Create metadata for the payment intent
            $metadata = [
                'user_id' => Auth::id(),
                'esim_ids' => implode(',', $esimIds),
                'coupon' => $coupon['code'] ?? '',
                'discount_amount' => $discount,
                'email' => $email,
            ];            

            // Create the payment intent (pass email as argument)
            $paymentIntent = $this->stripeService->createPaymentIntent($grandTotal, 'usd', $metadata, $email);

            return response()->json([
                'clientSecret' => $paymentIntent['clientSecret'],
                'paymentIntentId' => $paymentIntent['id'],
                'amount' => $paymentIntent['amount'],
                'currency' => $paymentIntent['currency'],
                'discount' => $discount,
                'coupon' => $coupon['code'] ?? null
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Process payment and create order
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processPayment(Request $request)
    {
        try {
            $paymentIntentId = $request->input('payment_intent_id');
            
            if (!$paymentIntentId) {
                return redirect()->route('payments.checkout')->with('error', 'Payment failed. Please try again.');
            }
            
            // Get cart items from session
            $cartItems = Session::get('cart', []);
            
            if (empty($cartItems)) {
                return redirect()->route('esims.index')->with('error', 'Your cart is empty.');
            }
            
            $total = 0;
            $esimDetails = [];

            $coupon = Session::get('coupon', null);
            $discount = 0;
            if ($coupon && isset($coupon['discount_percent'])) {
                $discount = $coupon['discount_percent'] / 100;
            }
            
            foreach ($cartItems as $id => $quantity) {
                $esim = Esim::find($id);
                if ($esim) {
                    $total += $esim->price();
                    $esimDetails[] = [
                        'id' => $esim->id,
                        'name' => $esim->name,
                        'price' => round($esim->price() - ($esim->price() * $discount), 2),
                        'product_id' => $esim->product_id,
                    ];
                }
            }
            
            // Validate request data for AJAX
            $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => $validator->errors()->first()
                ], 422);
            }

            $validatedData = $validator->validated();

            $user = User::where('email', $validatedData['email'])->first();
            if (!$user) {
                // Create Stripe customer
                $stripeCustomer = $this->stripeService->createCustomer([
                    'email' => $validatedData['email'],
                    'name' => $validatedData['name']
                ]);

                $user = User::create([
                    'name' => $validatedData['name'],
                    'email' => $validatedData['email'],
                    'password' => bcrypt(Str::random(8)),
                    'stripe_customer_id' => $stripeCustomer->id
                ]);
            } else if (!$user->stripe_customer_id) {
                // Create Stripe customer for existing user if they don't have one
                $stripeCustomer = $this->stripeService->createCustomer([
                    'email' => $user->email,
                    'name' => $user->name
                ]);
                
                $user->stripe_customer_id = $stripeCustomer->id;
                $user->save();
            }
            
            $orders = [];
            
            foreach ($esimDetails as $esimDetail) {
                $order = new Order();
                $order->user_id = $user->id;
                $order->esim_id = $esimDetail['id'];
                $order->order_number = 'ORD-' . strtoupper(Str::random(10));
                $order->amount = $esimDetail['price'];
                $order->payment_intent_id = $paymentIntentId;
                $order->payment_status = 'pending';
                $order->save();
                
                $orders[] = $order;
            }
            
            // Process the payment
            $paymentIntent = $this->stripeService->retrievePaymentIntent($paymentIntentId);

            if ($paymentIntent->status === 'succeeded') {
                
                foreach ($orders as $i => $order) {
                    $this->stripeService->processSuccessfulPayment($order, $paymentIntentId);
                    $airalo = $this->airaloService->purchaseEsim($esimDetails[$i]['product_id'], $user);
                    $order = Order::where('payment_intent_id', $paymentIntentId)->first();
                    $order->instructions = $airalo['data']['data']['qrcode_installation'];
                    $order->qr_code_url = $airalo['data']['data']['sims'][0]['qrcode_url'];
                    $order->iccid = $airalo['data']['data']['sims'][0]['iccid'];
                    $order->activation_code = $airalo['data']['data']['sims'][0]['matching_id'];
                    $order->save();
                }

                // Clear the cart
                Session::forget('cart');
                
                return redirect()->route('orders.confirmation', ['orderNumber' => $orders[0]->order_number])
                    ->with('success', 'Payment successful! Your eSIM has been purchased.');
            } else {
                return redirect()->route('payments.checkout')
                    ->with('error', 'Payment processing. Please check your orders for status updates.');
            }
        } catch (Exception $e) {
            return redirect()->route('payments.checkout')
                ->with('error', 'Payment failed: ' . $e->getMessage());
        }
    }

    /**
     * Show the payment success page
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function success(Request $request)
    {
        $paymentIntentId = $request->query('payment_intent');
        
        if (!$paymentIntentId) {
            return redirect()->route('home');
        }
        
        $order = Order::where('payment_intent_id', $paymentIntentId)->first();
        
        if (!$order) {
            return redirect()->route('home');
        }
        
        return view('payments.success', [
            'order' => $order
        ]);
    }

    /**
     * Show the payment canceled page
     *
     * @return \Illuminate\View\View
     */
    public function cancel()
    {
        return view('payments.cancel');
    }

    /**
     * Process payment using Stripe Confirmation Token (new flow)
     * Route: POST /payments/confirm
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processConfirmationToken(Request $request)
    {
        $confirmationToken = $request->input('confirmation_token');
        $cartItems = Session::get('cart', []);
        $coupon = Session::get('coupon', null);
        if (empty($cartItems)) {
            return response()->json(['error' => 'Your cart is empty.'], 400);
        }
        $total = 0;
        $esimDetails = [];
        $discount = 0;
        if ($coupon && isset($coupon['discount_percent'])) {
            $discount = $coupon['discount_percent'] / 100;
        }
        foreach ($cartItems as $id => $item) {
            $esim = Esim::find($id);
            if ($esim) {
                $total += $esim->price;
                $esimDetails[] = [
                    'id' => $esim->id,
                    'name' => $esim->name,
                    'price' => round($esim->price - ($esim->price * $discount), 2),
                    'product_id' => $esim->product_id,
                ];
            }
        }
        $tax = 0;
        $grandTotal = round($total - ($total * $discount) + $tax, 2);
        if ($grandTotal < 0) $grandTotal = 0;
        
        // Validate request data for AJAX
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => $validator->errors()->first()
            ], 422);
        }

        $validatedData = $validator->validated();


        $name = $request->input('name');
        $email = $request->input('email');
        
        if (!$name || !$email) {
            return response()->json(['error' => 'Name and email are required.'], 400);
        }
        try {
            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => (int)($grandTotal * 100),
                'currency' => 'usd',
                'confirmation_token' => $confirmationToken,
                'confirm' => true,
                'automatic_payment_methods' => ['enabled' => true],
                'receipt_email' => $email,
            ]);
            if ($paymentIntent->status !== 'succeeded') {
                return response()->json(['error' => 'Payment not completed. Status: ' . $paymentIntent->status], 400);
            }
            // User creation or lookup
            $user = User::where('email', $validatedData['email'])->first();
            if (!$user) {
                // Create Stripe customer
                $stripeCustomer = $this->stripeService->createCustomer([
                    'email' => $validatedData['email'],
                    'name' => $validatedData['name']
                ]);

                $user = User::create([
                    'name' => $validatedData['name'],
                    'email' => $validatedData['email'],
                    'password' => bcrypt(Str::random(8)),
                    'stripe_customer_id' => $stripeCustomer->id
                ]);
            } else if (!$user->stripe_customer_id) {
                // Create Stripe customer for existing user if they don't have one
                $stripeCustomer = $this->stripeService->createCustomer([
                    'email' => $user->email,
                    'name' => $user->name
                ]);
                
                $user->stripe_customer_id = $stripeCustomer->id;
                $user->save();
            }
            $orders = [];
            foreach ($esimDetails as $esimDetail) {
                $order = new Order();
                $order->user_id = $user->id;
                $order->esim_id = $esimDetail['id'];
                $order->order_number = 'ORD-' . strtoupper(Str::random(10));
                $order->amount = $esimDetail['price'];
                $order->payment_intent_id = $paymentIntent->id;
                $order->payment_status = 'paid';
                $order->save();
                $orders[] = $order;
            }
            // eSIM delivery (AiraloService)
            foreach ($orders as $i => $order) {
                $airalo = $this->airaloService->purchaseEsim($esimDetails[$i]['product_id'], $user);
                $order = Order::where('payment_intent_id', $paymentIntent->id)->first();
                $order->instructions = $airalo['data']['data']['qrcode_installation'] ?? null;
                $order->qr_code_url = $airalo['data']['data']['sims'][0]['qrcode_url'] ?? null;
                $order->iccid = $airalo['data']['data']['sims'][0]['iccid'] ?? null;
                $order->activation_code = $airalo['data']['data']['sims'][0]['matching_id'] ?? null;
                $order->save();
            }
            // Clear the cart
            Session::forget('cart');
            return response()->json([
                'clientSecret' => $paymentIntent->client_secret,
                'paymentIntentId' => $paymentIntent->id,
                'status' => $paymentIntent->status,
                'orderNumber' => $orders[0]->order_number ?? null,
            ]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
