<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Order;
use App\Services\StripeService;
use Stripe\Event;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Exception;

class WebhookController extends Controller
{
    protected $stripeService;

    /**
     * Create a new controller instance.
     *
     * @param StripeService $stripeService
     */
    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Handle Stripe webhook events
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function handleStripeWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $webhookSecret = config('services.stripe.webhook.secret');

        try {
            // Verify the webhook signature
            $event = Webhook::constructEvent(
                $payload,
                $sigHeader,
                $webhookSecret
            );

            // Handle the event based on its type
            switch ($event->type) {
                case 'payment_intent.succeeded':
                    return $this->handlePaymentIntentSucceeded($event);
                case 'payment_intent.payment_failed':
                    return $this->handlePaymentIntentFailed($event);
                case 'charge.refunded':
                    return $this->handleChargeRefunded($event);
                default:
                    // Log unhandled events
                    Log::info('Unhandled Stripe event: ' . $event->type);
                    return response()->json(['status' => 'success']);
            }
        } catch (SignatureVerificationException $e) {
            // Invalid signature
            Log::error('Webhook signature verification failed: ' . $e->getMessage());
            return response()->json(['error' => 'Invalid signature'], 400);
        } catch (Exception $e) {
            // Other errors
            Log::error('Webhook error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Handle payment_intent.succeeded event
     *
     * @param Event $event
     * @return \Illuminate\Http\Response
     */
    private function handlePaymentIntentSucceeded(Event $event)
    {
        $paymentIntent = $event->data->object;
        $paymentIntentId = $paymentIntent->id;

        // Find orders with this payment intent
        $orders = Order::where('payment_intent_id', $paymentIntentId)
            ->where('payment_status', '!=', 'paid')
            ->get();

        if ($orders->isEmpty()) {
            Log::info('No pending orders found for payment intent: ' . $paymentIntentId);
            return response()->json(['status' => 'success']);
        }

        // Update each order
        foreach ($orders as $order) {
            try {
                $this->stripeService->processSuccessfulPayment($order, $paymentIntentId);
                Log::info('Order ' . $order->order_number . ' marked as paid via webhook');
            } catch (Exception $e) {
                Log::error('Failed to process order ' . $order->order_number . ': ' . $e->getMessage());
            }
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Handle payment_intent.payment_failed event
     *
     * @param Event $event
     * @return \Illuminate\Http\Response
     */
    private function handlePaymentIntentFailed(Event $event)
    {
        $paymentIntent = $event->data->object;
        $paymentIntentId = $paymentIntent->id;

        // Find orders with this payment intent
        $orders = Order::where('payment_intent_id', $paymentIntentId)->get();

        foreach ($orders as $order) {
            $order->payment_status = 'failed';
            $order->save();
            Log::info('Order ' . $order->order_number . ' marked as failed via webhook');
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Handle charge.refunded event
     *
     * @param Event $event
     * @return \Illuminate\Http\Response
     */
    private function handleChargeRefunded(Event $event)
    {
        $charge = $event->data->object;
        $paymentIntentId = $charge->payment_intent;

        // Find orders with this payment intent
        $orders = Order::where('payment_intent_id', $paymentIntentId)
            ->where('payment_status', 'paid')
            ->get();

        foreach ($orders as $order) {
            $order->payment_status = 'refunded';
            $order->save();
            Log::info('Order ' . $order->order_number . ' marked as refunded via webhook');
        }

        return response()->json(['status' => 'success']);
    }
}
