<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Esim;

class Country extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'name',
        'code',
        'slug',
        'flag_url',
        'description',
        'is_popular',
        'price_from',
    ];
    
    protected $casts = [
        'is_popular' => 'boolean',
        'price_from' => 'decimal:2',
    ];
    
    /**
     * Get the eSIMs for the country.
     */
    public function esims()
    {
        return $this->hasMany(Esim::class);
    }
}
