<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Country;
use App\Models\Order;

class Esim extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'country_id',
        'name',
        'operator',
        'product_id',
        'description',
        'price',
        'data_amount',
        'validity_days',
        'features',
    ];
    
    protected $casts = [
        'price' => 'decimal:2',
        'validity_days' => 'integer',
        'features' => 'array',
    ];
    
    /**
     * Get the price of the eSIM.
     */
    public function price(){
        return round($this->price-1).'.99';
    }
    /**
     * Get the country that owns the eSIM.
     */
    public function country()
    {
        return $this->belongsTo(Country::class);
    }
    
    /**
     * Get the orders for the eSIM.
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }
}
