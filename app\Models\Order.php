<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\Esim;

class Order extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'user_id',
        'esim_id',
        'order_number',
        'amount',
        'payment_intent_id',
        'payment_status',
        'qr_code_url',
        'activation_code',
        'instructions',
    ];
    
    protected $casts = [
        'amount' => 'decimal:2',
    ];
    
    /**
     * Get the user that owns the order.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * Get the eSIM that belongs to the order.
     */
    public function esim()
    {
        return $this->belongsTo(Esim::class);
    }
}
