<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class LoginLink extends Notification
{
    protected $token;

    /**
     * Create a new notification instance.
     *
     * <AUTHOR> <https://gempixel.com>
     * @version 1.0
     * @param string $token
     * @return void
     */
    public function __construct($token)
    {
        $this->token = $token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * <AUTHOR> <https://gempixel.com>
     * @version 1.0
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * <AUTHOR> <https://gempixel.com>
     * @version 1.0
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Your Login Link')
            ->greeting('Hello!')
            ->line('You are receiving this email because we received a login request for your account.')
            ->action('Login Now', url('/login/verify/' . $this->token))
            ->line('This login link will expire in 60 minutes.')
            ->line('If you did not request a login link, no further action is required.');
    }
} 