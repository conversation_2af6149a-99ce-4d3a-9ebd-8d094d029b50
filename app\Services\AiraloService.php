<?php

namespace App\Services;

use Airalo\Airalo;
use Illuminate\Support\Facades\Log;

class AiraloService
{
    protected $client;

    public function __construct()
    {
        $this->client = new Airalo([
            'client_id' => config('services.airalo.key'),
            'client_secret' => config('services.airalo.secret'),
            'env' => env('AIRALO_ENV', 'production')
        ]);
    }

    /**
     * Get all countries with available eSIMs
     *
     * @return array
     */
    public function getCountries()
    {
        try {
            $response = $this->client->getGlobalPackages(false);
            return $response->data;
        } catch (AiraloException $e) {
            Log::error('Error fetching countries from Airalo API', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);
            return [];
        }
    }

    /**
     * Get eSIMs available for a specific country
     *
     * @param string $countryCode
     * @return array
     */
    public function getEsimsByCountry($countryCode)
    {
        try {
            $response = $this->client->getCountryPackages($countryCode);
            return $response->data;
        } catch (AiraloException $e) {
            Log::error('Error fetching eSIMs for country from Airalo API', [
                'country' => $countryCode,
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);
            return [];
        }
    }
    /**
     * Purchase an eSIM
     *
     * @param string $productId
     * @param array $userData
     * @return array
     */
    public function purchaseEsim($productId, $userData)
    {
        try {
            $response = $this->client->orderWithEmailSimShare($productId, 1, [
                'to_email' => $userData->email,
                'sharing_option' => ['link', 'pdf'],
                'copy_address' => ['<EMAIL>'],
                'brand_settings_name' => 'DataLendR'
            ]);
            
            return [
                'success' => true,
                'data' => $response,
            ];
        } catch (AiraloException $e) {
            Log::error('Error purchasing eSIM from Airalo API', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
            ]);
            
            return [
                'success' => false,
                'message' => 'Failed to purchase eSIM: ' . $e->getMessage(),
            ];
        }
    }
}
