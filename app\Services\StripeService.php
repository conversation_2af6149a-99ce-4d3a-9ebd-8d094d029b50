<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;
use App\Models\Order;
use Exception;

class StripeService
{
    /**
     * Initialize Stripe with the API key
     */
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a payment intent for the order
     *
     * @param float $amount Amount to charge in dollars
     * @param string $currency Currency code (default: USD)
     * @param array $metadata Additional metadata for the payment intent
     * @param string|null $email The user's email to include in the payment intent
     * @return array Payment intent details
     * @throws Exception
     */
    public function createPaymentIntent(float $amount, string $currency = 'usd', array $metadata = [], string $email = null)
    {
        try {
            // Convert amount to cents (Stripe requires amount in smallest currency unit)
            $amountInCents = (int) ($amount * 100);
            
            $params = [
                'amount' => $amountInCents,
                'currency' => $currency,
                'metadata' => $metadata
            ];
            if ($email) {
                $params['receipt_email'] = $email;
            }
            $paymentIntent = PaymentIntent::create($params);

            return [
                'clientSecret' => $paymentIntent->client_secret,
                'id' => $paymentIntent->id,
                'amount' => $amount,
                'currency' => $currency
            ];
        } catch (ApiErrorException $e) {
            throw new Exception('Stripe API Error: ' . $e->getMessage());
        }
    }

    /**
     * Retrieve a payment intent
     *
     * @param string $paymentIntentId The payment intent ID
     * @return PaymentIntent
     * @throws Exception
     */
    public function retrievePaymentIntent(string $paymentIntentId)
    {
        try {
            return PaymentIntent::retrieve($paymentIntentId);
        } catch (ApiErrorException $e) {
            throw new Exception('Stripe API Error: ' . $e->getMessage());
        }
    }

    /**
     * Confirm the payment intent
     *
     * @param string $paymentIntentId The payment intent ID
     * @param string $paymentMethodId The payment method ID
     * @return PaymentIntent
     * @throws Exception
     */
    public function confirmPaymentIntent(string $paymentIntentId, string $paymentMethodId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            $paymentIntent->confirm([
                'payment_method' => $paymentMethodId,
            ]);
            
            return $paymentIntent;
        } catch (ApiErrorException $e) {
            throw new Exception('Stripe API Error: ' . $e->getMessage());
        }
    }

    /**
     * Process a successful payment and update the order
     *
     * @param Order $order The order to update
     * @param string $paymentIntentId The payment intent ID
     * @return Order
     * @throws Exception
     */
    public function processSuccessfulPayment(Order $order, string $paymentIntentId)
    {
        try {
            $paymentIntent = $this->retrievePaymentIntent($paymentIntentId);
            
            // Check if payment is successful
            if ($paymentIntent->status === 'succeeded') {
                $order->payment_status = 'paid';
                $order->payment_intent_id = $paymentIntentId;
                $order->save();            
                
                return $order;
            } else {
                throw new Exception('Payment has not been completed. Status: ' . $paymentIntent->status);
            }
        } catch (ApiErrorException $e) {
            throw new Exception('Stripe API Error: ' . $e->getMessage());
        }
    }

    /**
     * Create a new customer in Stripe
     *
     * @param array $customerData Customer data including email and name
     * @return \Stripe\Customer
     * @throws Exception
     */
    public function createCustomer(array $customerData)
    {
        try {
            return \Stripe\Customer::create([
                'email' => $customerData['email'],
                'name' => $customerData['name'],
                'metadata' => [
                    'user_id' => $customerData['user_id'] ?? null
                ]
            ]);
        } catch (ApiErrorException $e) {
            throw new Exception('Stripe API Error: ' . $e->getMessage());
        }
    }
}
