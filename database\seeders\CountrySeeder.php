<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Country;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
            [
                'name' => 'United States',
                'code' => 'us',
                'flag_url' => 'https://flagcdn.com/w320/us.png',
                'description' => 'Stay connected across the United States with our high-speed data plans.',
                'price_from' => 14.99,
                'is_popular' => true,
            ],
            [
                'name' => 'United Kingdom',
                'code' => 'gb',
                'flag_url' => 'https://flagcdn.com/w320/gb.png',
                'description' => 'Explore the UK with reliable connectivity throughout England, Scotland, Wales, and Northern Ireland.',
                'price_from' => 12.99,
                'is_popular' => true,
            ],
            [
                'name' => 'Japan',
                'code' => 'jp',
                'flag_url' => 'https://flagcdn.com/w320/jp.png',
                'description' => 'Navigate Japan with ease using our high-speed data plans covering Tokyo, Kyoto, Osaka, and beyond.',
                'price_from' => 16.99,
                'is_popular' => true,
            ],
            [
                'name' => 'France',
                'code' => 'fr',
                'flag_url' => 'https://flagcdn.com/w320/fr.png',
                'description' => 'Enjoy seamless connectivity throughout France, from Paris to the French Riviera.',
                'price_from' => 13.99,
                'is_popular' => true,
            ],
            [
                'name' => 'Italy',
                'code' => 'it',
                'flag_url' => 'https://flagcdn.com/w320/it.png',
                'description' => 'Stay connected in Italy with coverage from Rome to Venice, Florence to the Amalfi Coast.',
                'price_from' => 13.99,
                'is_popular' => true,
            ],
            [
                'name' => 'Spain',
                'code' => 'es',
                'flag_url' => 'https://flagcdn.com/w320/es.png',
                'description' => 'Explore Spain with reliable connectivity from Barcelona to Madrid, Seville to Valencia.',
                'price_from' => 12.99,
                'is_popular' => true,
            ],
            [
                'name' => 'Germany',
                'code' => 'de',
                'flag_url' => 'https://flagcdn.com/w320/de.png',
                'description' => 'Connect throughout Germany with coverage in Berlin, Munich, Frankfurt, and beyond.',
                'price_from' => 13.99,
                'is_popular' => true,
            ],
            [
                'name' => 'Australia',
                'code' => 'au',
                'flag_url' => 'https://flagcdn.com/w320/au.png',
                'description' => 'Stay connected across Australia from Sydney to Perth, Melbourne to the Gold Coast.',
                'price_from' => 15.99,
                'is_popular' => true,
            ],
            [
                'name' => 'Canada',
                'code' => 'ca',
                'flag_url' => 'https://flagcdn.com/w320/ca.png',
                'description' => 'Explore Canada with reliable connectivity from Toronto to Vancouver, Montreal to Calgary.',
                'price_from' => 14.99,
                'is_popular' => true,
            ],
            [
                'name' => 'Singapore',
                'code' => 'sg',
                'flag_url' => 'https://flagcdn.com/w320/sg.png',
                'description' => 'Experience ultra-fast connectivity throughout Singapore with our premium data plans.',
                'price_from' => 10.99,
                'is_popular' => true,
            ],
        ];

        foreach ($countries as $country) {
            Country::updateOrCreate(
                ['code' => $country['code']],
                $country
            );
        }
    }
}
