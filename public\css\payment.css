/**
 * eSIMConnect Payment Styles
 * Custom styles for the payment pages
 */

/* Stripe Elements container */
.stripe-element-container {
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 0.75rem;
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.stripe-element-container:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading spinner */
.spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto;
    border-radius: 50%;
    border: 3px solid rgba(59, 130, 246, 0.2);
    border-top-color: #3b82f6;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Payment status badges */
.payment-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.payment-status-paid {
    background-color: #dcfce7;
    color: #166534;
}

.payment-status-pending {
    background-color: #fef9c3;
    color: #854d0e;
}

.payment-status-failed {
    background-color: #fee2e2;
    color: #b91c1c;
}

.payment-status-refunded {
    background-color: #e0e7ff;
    color: #3730a3;
}

/* Card icon styles */
.card-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.card-visa {
    color: #1a1f71;
}

.card-mastercard {
    color: #eb001b;
}

.card-amex {
    color: #006fcf;
}

.card-discover {
    color: #ff6600;
}

/* QR code container */
.qr-code-container {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    display: inline-block;
}

/* Activation code display */
.activation-code {
    font-family: monospace;
    letter-spacing: 0.1em;
    font-size: 1.25rem;
    font-weight: 600;
    background-color: #f3f4f6;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    display: inline-block;
}

/* Payment method selection */
.payment-method-option {
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.payment-method-option:hover {
    border-color: #cbd5e1;
    background-color: #f8fafc;
}

.payment-method-option.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .payment-summary {
        position: static !important;
        margin-bottom: 2rem;
    }
}

[role=alert] {
    position: fixed;
    top: 10px;
    left: 10px;
    box-shadow: 0 10px 10px rgba(0,0,0,0.5);
    animation: 5s autohide linear;
    opacity: 0;
    display: none;
}
@keyframes autohide {
  0% {
      opacity: 1;
      display: block;
  }
  99% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    display: none;
  }
}