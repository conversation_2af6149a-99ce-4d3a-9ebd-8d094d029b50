/**
 * Stripe Elements CSS
 * Custom styling for Stripe Elements components
 */

.StripeElement {
    box-sizing: border-box;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background-color: white;
    transition: box-shadow 150ms ease, border 150ms ease;
}

.StripeElement--focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
    border-color: #3b82f6;
}

.StripeElement--invalid {
    border-color: #ef4444;
}

.StripeElement--webkit-autofill {
    background-color: #fefde5 !important;
}

/* Card Element container */
#card-element {
    margin-bottom: 24px;
}

/* Error message styling */
#card-errors {
    color: #ef4444;
    font-size: 14px;
    margin-top: 8px;
    margin-bottom: 16px;
}

/* Success message styling */
.payment-success-message {
    color: #10b981;
    font-size: 14px;
    margin-top: 8px;
    margin-bottom: 16px;
}

/* Payment form styling */
.payment-form {
    max-width: 500px;
    margin: 0 auto;
}

.payment-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1f2937;
}

.payment-form .form-row {
    margin-bottom: 16px;
}

/* Payment button styling */
#payment-submit {
    background-color: #f59e0b;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    transition: background-color 0.3s ease;
}

#payment-submit:hover {
    background-color: #d97706;
}

#payment-submit:disabled {
    background-color: #d1d5db;
    cursor: not-allowed;
}

/* Processing state */
.processing {
    opacity: 0.7;
    pointer-events: none;
}

.processing::after {
    content: "Processing...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #1f2937;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 8px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
