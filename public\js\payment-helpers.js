/**
 * eSIMConnect Payment Helpers
 * Utility functions for enhancing the payment experience
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize payment-related functionality
    initPaymentSummary();
    initPaymentMethodSelection();
    initPaymentStatusIndicators();
});

/**
 * Initialize the sticky payment summary on checkout page
 */
function initPaymentSummary() {
    const paymentSummary = document.querySelector('.payment-summary');
    
    if (paymentSummary) {
        // Make the payment summary sticky on desktop
        if (window.innerWidth >= 1024) {
            const header = document.querySelector('header');
            const headerHeight = header ? header.offsetHeight : 0;
            
            window.addEventListener('scroll', function() {
                if (window.scrollY > headerHeight) {
                    paymentSummary.classList.add('sticky-top');
                    paymentSummary.style.top = '1rem';
                } else {
                    paymentSummary.classList.remove('sticky-top');
                    paymentSummary.style.top = '0';
                }
            });
        }
    }
}

/**
 * Initialize payment method selection functionality
 */
function initPaymentMethodSelection() {
    const paymentMethodOptions = document.querySelectorAll('.payment-method-option');
    
    if (paymentMethodOptions.length) {
        paymentMethodOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                paymentMethodOptions.forEach(opt => opt.classList.remove('selected'));
                
                // Add selected class to clicked option
                this.classList.add('selected');
                
                // Update hidden input value
                const paymentMethodInput = document.getElementById('payment_method');
                if (paymentMethodInput) {
                    paymentMethodInput.value = this.dataset.method;
                }
            });
        });
    }
}

/**
 * Initialize payment status indicators
 */
function initPaymentStatusIndicators() {
    const statusElements = document.querySelectorAll('[data-payment-status]');
    
    if (statusElements.length) {
        statusElements.forEach(element => {
            const status = element.dataset.paymentStatus;
            
            // Add appropriate class based on status
            switch (status) {
                case 'paid':
                case 'completed':
                    element.classList.add('payment-status', 'payment-status-paid');
                    break;
                case 'pending':
                    element.classList.add('payment-status', 'payment-status-pending');
                    break;
                case 'failed':
                    element.classList.add('payment-status', 'payment-status-failed');
                    break;
                case 'refunded':
                    element.classList.add('payment-status', 'payment-status-refunded');
                    break;
            }
        });
    }
}

/**
 * Format a price for display
 * 
 * @param {number} price - Price to format
 * @param {string} currency - Currency code (default: USD)
 * @returns {string} Formatted price
 */
function formatPrice(price, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(price);
}

/**
 * Format a credit card number for display
 * 
 * @param {string} cardNumber - Card number to format
 * @returns {string} Formatted card number
 */
function formatCardNumber(cardNumber) {
    // Only show last 4 digits
    return `•••• •••• •••• ${cardNumber.slice(-4)}`;
}

/**
 * Get the card type based on the card number
 * 
 * @param {string} cardNumber - Card number to check
 * @returns {string} Card type (visa, mastercard, amex, discover, or unknown)
 */
function getCardType(cardNumber) {
    // Remove all non-digit characters
    const cleanNumber = cardNumber.replace(/\D/g, '');
    
    // Check card type based on prefix
    if (/^4/.test(cleanNumber)) {
        return 'visa';
    } else if (/^5[1-5]/.test(cleanNumber)) {
        return 'mastercard';
    } else if (/^3[47]/.test(cleanNumber)) {
        return 'amex';
    } else if (/^6(?:011|5)/.test(cleanNumber)) {
        return 'discover';
    } else {
        return 'unknown';
    }
}

/**
 * Get the card icon class based on the card type
 * 
 * @param {string} cardType - Card type
 * @returns {string} Font Awesome icon class
 */
function getCardIcon(cardType) {
    switch (cardType) {
        case 'visa':
            return 'fab fa-cc-visa card-visa';
        case 'mastercard':
            return 'fab fa-cc-mastercard card-mastercard';
        case 'amex':
            return 'fab fa-cc-amex card-amex';
        case 'discover':
            return 'fab fa-cc-discover card-discover';
        default:
            return 'fas fa-credit-card';
    }
}
