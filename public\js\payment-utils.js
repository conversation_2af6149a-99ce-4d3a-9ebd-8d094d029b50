/**
 * eSIMConnect Payment Utilities
 * Helper functions for payment processing
 */

const PaymentUtils = {
    /**
     * Format a price for display
     * 
     * @param {number} price - Price to format
     * @param {string} currency - Currency code (default: USD)
     * @returns {string} Formatted price
     */
    formatPrice: function(price, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(price);
    },

    /**
     * Format a credit card number for display
     * 
     * @param {string} cardNumber - Card number to format
     * @returns {string} Formatted card number
     */
    formatCardNumber: function(cardNumber) {
        // Only show last 4 digits
        return `•••• •••• •••• ${cardNumber.slice(-4)}`;
    },

    /**
     * Get the card type based on the card number
     * 
     * @param {string} cardNumber - Card number to check
     * @returns {string} Card type (visa, mastercard, amex, discover, or unknown)
     */
    getCardType: function(cardNumber) {
        // Remove all non-digit characters
        const cleanNumber = cardNumber.replace(/\D/g, '');
        
        // Check card type based on prefix
        if (/^4/.test(cleanNumber)) {
            return 'visa';
        } else if (/^5[1-5]/.test(cleanNumber)) {
            return 'mastercard';
        } else if (/^3[47]/.test(cleanNumber)) {
            return 'amex';
        } else if (/^6(?:011|5)/.test(cleanNumber)) {
            return 'discover';
        } else {
            return 'unknown';
        }
    },

    /**
     * Get the card icon class based on the card type
     * 
     * @param {string} cardType - Card type
     * @returns {string} Font Awesome icon class
     */
    getCardIcon: function(cardType) {
        switch (cardType) {
            case 'visa':
                return 'fab fa-cc-visa';
            case 'mastercard':
                return 'fab fa-cc-mastercard';
            case 'amex':
                return 'fab fa-cc-amex';
            case 'discover':
                return 'fab fa-cc-discover';
            default:
                return 'fas fa-credit-card';
        }
    },

    /**
     * Validate a credit card number using the Luhn algorithm
     * 
     * @param {string} cardNumber - Card number to validate
     * @returns {boolean} Whether the card number is valid
     */
    validateCardNumber: function(cardNumber) {
        // Remove all non-digit characters
        const cleanNumber = cardNumber.replace(/\D/g, '');
        
        // Check if the number is of valid length
        if (cleanNumber.length < 13 || cleanNumber.length > 19) {
            return false;
        }
        
        // Luhn algorithm
        let sum = 0;
        let shouldDouble = false;
        
        // Loop through the digits in reverse order
        for (let i = cleanNumber.length - 1; i >= 0; i--) {
            let digit = parseInt(cleanNumber.charAt(i));
            
            if (shouldDouble) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }
            
            sum += digit;
            shouldDouble = !shouldDouble;
        }
        
        return sum % 10 === 0;
    }
};

// Make the utilities available globally
window.PaymentUtils = PaymentUtils;
