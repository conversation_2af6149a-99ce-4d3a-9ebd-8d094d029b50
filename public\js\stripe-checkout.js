/**
 * eSIMConnect Stripe Checkout Integration
 * Handles the Stripe Elements integration for payment processing
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const paymentForm = document.getElementById('payment-form');
    const submitButton = document.getElementById('submit-button');
    const spinner = document.getElementById('spinner');
    const buttonText = document.getElementById('button-text');
    const paymentElement = document.getElementById('payment-element');
    const errorElement = document.getElementById('card-errors');
    
    // Get Stripe publishable key and client secret from data attributes
    const stripeKey = paymentElement.dataset.stripeKey;
    
    if (!stripeKey) {
        console.error('Stripe key is missing');
        errorElement.textContent = 'Configuration error: Stripe key is missing';
        return;
    }
    
    // Initialize Stripe
    const stripe = Stripe(stripeKey);
    let elements;
    
    // Helper: log to console and show in error div
    function debugLog(msg) {
        return;
        console.log('[StripeCheckout]', msg);        
    }

    // Create payment intent when page loads
    createPaymentIntent();
    // Expose for coupon refresh
    window.refreshStripePaymentForm = createPaymentIntent;

    async function createPaymentIntent() {
        try {
            debugLog('Creating payment intent...');
            paymentElement.innerHTML = '<div class="p-4 text-center"><div class="spinner"></div><p class="mt-2 text-gray-600">Loading payment form...</p></div>';
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            if (!csrfToken) {
                debugLog('CSRF token missing from meta tag.');
                return;
            }
            const response = await fetch('/payments/intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            });
            if (!response.ok) {
                const errorData = await response.json();
                debugLog('Payment intent AJAX error: ' + (errorData.error || 'Unknown error'));
                return;
            }
            const data = await response.json();
            const { clientSecret, paymentIntentId, amount } = data;
            if (!clientSecret || !paymentIntentId) {
                return;
            }
            document.getElementById('payment_intent_id').value = paymentIntentId;
            elements = stripe.elements({
                clientSecret,
                appearance: {
                    theme: 'stripe',
                    variables: {
                        colorPrimary: '#3B82F6',
                        colorBackground: '#ffffff',
                        colorText: '#1F2937',
                        colorDanger: '#EF4444',
                        fontFamily: 'system-ui, -apple-system, "Segoe UI", Roboto, sans-serif',
                        spacingUnit: '4px',
                        borderRadius: '4px'
                    }
                }
            });
            const stripePaymentElement = elements.create('payment');
            stripePaymentElement.mount('#payment-element');
            submitButton.disabled = false;
            // Update the button text with the new amount
            if (buttonText && typeof amount !== 'undefined') {
                buttonText.textContent = `Pay $${parseFloat(amount).toFixed(2)}`;
            }
        } catch (error) {
            debugLog('Error in createPaymentIntent: ' + (error.message || error));
            paymentElement.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load payment form. Please refresh the page.</div>';
        }
    }

    // Unified handler for payment submission
    async function handlePaymentSubmission(e) {
        e.preventDefault();
        debugLog('Pay button clicked. Starting payment confirmation...');

        // check to make sure the name field and email field are filled out
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        if (!name || !email) {
            debugLog('Name or email is missing');
            document.getElementById('email-errors').textContent = 'Please fill out your name and email';
            document.getElementById('email-errors').classList.remove('hidden');
            return;
        }
        document.getElementById('email-errors').classList.add('hidden');
        submitButton.disabled = true;
        spinner.classList.remove('hidden');
        buttonText.classList.add('hidden');
        try {
            
            
            const {error: submitError} = await elements.submit();

            if (submitError) {					
                debugLog('Stripe createConfirmationToken error: ' + submitError.message);
                document.getElementById('email-errors').textContent = submitError.message;
                document.getElementById('email-errors').classList.remove('hidden');
                submitButton.disabled = false;
                spinner.classList.add('hidden');
                buttonText.classList.remove('hidden');
                return;
            }

            // Create the ConfirmationToken using Stripe.js
            const { error, confirmationToken } = await stripe.createConfirmationToken({
                elements,
                params: {
                    payment_method_data: {
                        billing_details: {
                            name: name,
                            email: email,
                        }
                    },
                    return_url: window.location.origin + '/payments/success',
                }
            });
            if (error) {
                debugLog('Stripe createConfirmationToken error: ' + error.message);
                document.getElementById('email-errors').textContent = error.message;
                document.getElementById('email-errors').classList.remove('hidden');
                submitButton.disabled = false;
                spinner.classList.add('hidden');
                buttonText.classList.remove('hidden');
                return;
            }
            // Send confirmationToken.id, name, and email to backend
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            const response = await fetch('/payments/confirm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    confirmation_token: confirmationToken.id,
                    name: name,
                    email: email
                })
            });
            const data = await response.json();
            if (data.error) {
                debugLog('Backend error: ' + data.error);
                document.getElementById('email-errors').textContent = data.error;
                document.getElementById('email-errors').classList.remove('hidden');
                submitButton.disabled = false;
                spinner.classList.add('hidden');
                buttonText.classList.remove('hidden');
                return;
            }
            // On success, redirect to the success page or submit the form
            window.location.href = '/confirmation/' + data.orderNumber;
        } catch (error) {
            debugLog('Fatal error during payment: ' + (error.message || error));
            document.getElementById('email-errors').textContent = error.message || error;
            document.getElementById('email-errors').classList.remove('hidden');
            submitButton.disabled = false;
            spinner.classList.add('hidden');
            buttonText.classList.remove('hidden');
        }
    }

    // Always bind click event to submit button
    if (submitButton) {
        submitButton.addEventListener('click', handlePaymentSubmission);
    }
    // Also bind to form submit if paymentForm exists
    if (paymentForm) {
        paymentForm.addEventListener('submit', handlePaymentSubmission);
    }
});
