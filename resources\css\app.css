@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer components {
    .btn-primary {
        @apply bg-secondary hover:bg-secondary/90 text-white font-bold py-2 px-4 rounded-lg transition duration-300;
    }
    
    .btn-secondary {
        @apply bg-white hover:bg-gray-100 text-secondary border border-secondary font-bold py-2 px-4 rounded-lg transition duration-300;
    }
    
    .card {
        @apply bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:scale-105;
    }
    
    .input-field {
        @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent;
    }
}