@extends('admin.dashboard')

@section('content')
<div class="flex min-h-screen">
    @include('admin.partials.sidebar')
    <main class="flex-1 bg-gray-50 p-8">
        <h1 class="text-2xl font-bold mb-6">Edit Blog Post</h1>
        <form action="{{ route('admin.blog.update', $blog->id) }}" method="POST" class="space-y-6 max-w-lg">
            @csrf
            @method('PUT')
            <div>
                <label class="block mb-1 font-semibold">Title</label>
                <input type="text" name="title" value="{{ old('title', $blog->title) }}" class="w-full border rounded px-3 py-2" required>
            </div>
            <div>
                <label class="block">Image</label>
                <input type="text" name="image" class="border rounded w-full p-2" value="{{ old('image', $blog->image) }}" required>
            </div> 
            <div>
                <label class="block mb-1 font-semibold">Content</label>
                <textarea name="content" class="w-full border rounded px-3 py-2" rows="5" required>{{ old('content', $blog->content) }}</textarea>
            </div>
            <div>
                <label class="block mb-1 font-semibold">Published</label>
                <select name="published" class="w-full border rounded px-3 py-2">
                    <option value="0" {{ !$blog->published ? 'selected' : '' }}>No</option>
                    <option value="1" {{ $blog->published ? 'selected' : '' }}>Yes</option>
                </select>
            </div>
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded">Update Blog Post</button>
        </form>
    </main>
</div>
@endsection
