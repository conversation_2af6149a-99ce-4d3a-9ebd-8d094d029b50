@extends('admin.dashboard')

@section('content')
<div class="flex min-h-screen">
    @include('admin.partials.sidebar')
    <main class="flex-1 bg-gray-50 p-8">
        <div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-bold">Manage Countries</h1>
    <a href="{{ route('admin.countries.create') }}" class="bg-yellow-500 text-black px-4 py-2 rounded shadow">+ Create Country</a>
</div>
        <table class="min-w-full bg-white rounded shadow">
            <thead>
                <tr>
                    <th class="py-2 px-4">ID</th>
                    <th class="py-2 px-4">Name</th>
                    <th class="py-2 px-4">Code</th>
                    <th class="py-2 px-4">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($countries as $country)
                    <tr>
                        <td class="py-2 px-4">{{ $country->id }}</td>
                        <td class="py-2 px-4">{{ $country->name }}</td>
                        <td class="py-2 px-4">{{ $country->code }}</td>
                        <td class="py-2 px-4">
                            <a href="{{ route('admin.countries.edit', $country->id) }}" class="text-blue-500">Edit</a> |
                            <form action="{{ route('admin.countries.destroy', $country->id) }}" method="POST" style="display:inline;">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-500 bg-transparent border-none cursor-pointer p-0 m-0" onclick="return confirm('Are you sure you want to delete this country?');">Delete</button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </main>
</div>
@endsection
