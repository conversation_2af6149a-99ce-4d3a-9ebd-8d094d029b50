@extends('admin.dashboard')

@section('content')
<div class="flex min-h-screen">
    @include('admin.partials.sidebar')
    <main class="flex-1 bg-gray-50 p-8">
        <h1 class="text-2xl font-bold mb-6">Edit eSIM</h1>
        <form action="{{ route('admin.esims.update', $esim->id) }}" method="POST" class="space-y-6 max-w-lg">
            @csrf
            @method('PUT')
            <div>
                <label class="block mb-1 font-semibold">Name</label>
                <input type="text" name="name" value="{{ old('name', $esim->name) }}" class="w-full border rounded px-3 py-2" required>
            </div>
            <div>
                <label class="block mb-1 font-semibold">Country</label>
                <select name="country_id" class="w-full border rounded px-3 py-2">
                    @foreach($countries as $country)
                        <option value="{{ $country->id }}" {{ $esim->country_id == $country->id ? 'selected' : '' }}>{{ $country->name }}</option>
                    @endforeach
                </select>
            </div>
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded">Update eSIM</button>
        </form>
    </main>
</div>
@endsection
