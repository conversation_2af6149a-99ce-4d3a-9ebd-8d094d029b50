@extends('admin.dashboard')

@section('content')
<div class="flex min-h-screen">
    @include('admin.partials.sidebar')
    <main class="flex-1 bg-gray-50 p-8">
        <div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-bold">Manage Help Articles</h1>
    <a href="{{ route('admin.help.create') }}" class="bg-yellow-500 text-black px-4 py-2 rounded shadow">+ Create Help Article</a>
</div>
        <table class="min-w-full bg-white rounded shadow">
            <thead>
                <tr>
                    <th class="py-2 px-4">ID</th>
                    <th class="py-2 px-4">Title</th>
                    <th class="py-2 px-4">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($helps as $help)
                    <tr>
                        <td class="py-2 px-4">{{ $help->id }}</td>
                        <td class="py-2 px-4">{{ $help->title }}</td>
                        <td class="py-2 px-4">
                            <a href="{{ route('admin.help.edit', $help->id) }}" class="text-blue-500">Edit</a> |
                            <form action="{{ route('admin.help.destroy', $help->id) }}" method="POST" style="display:inline;">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-500 bg-transparent border-none cursor-pointer p-0 m-0" onclick="return confirm('Are you sure you want to delete this help article?');">Delete</button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </main>
</div>
@endsection
