@extends('admin.dashboard')

@section('content')
<div class="flex min-h-screen">
    @include('admin.partials.sidebar')
    <main class="flex-1 bg-gray-50 p-8">
        <h1 class="text-2xl font-bold mb-6">Add User</h1>
        <form method="POST" action="{{ route('admin.users.store') }}" class="space-y-4">
            @csrf
            <div>
                <label class="block">Name</label>
                <input type="text" name="name" class="border rounded w-full p-2" required>
            </div>
            <div>
                <label class="block">Email</label>
                <input type="email" name="email" class="border rounded w-full p-2" required>
            </div>
            <div>
                <label class="block">Password</label>
                <input type="password" name="password" class="border rounded w-full p-2" required>
            </div>
            <div>
                <label class="block">Confirm Password</label>
                <input type="password" name="password_confirmation" class="border rounded w-full p-2" required>
            </div>
            <div>
                <label class="inline-flex items-center">
                    <input type="checkbox" name="is_admin" value="1" class="form-checkbox">
                    <span class="ml-2">Admin</span>
                </label>
            </div>
            <button type="submit" class="bg-yellow-500 text-black px-4 py-2 rounded">Create</button>
        </form>
    </main>
</div>  
@endsection
