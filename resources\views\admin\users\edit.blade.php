@extends('admin.dashboard')

@section('content')
<div class="flex min-h-screen">
    @include('admin.partials.sidebar')
    <main class="flex-1 bg-gray-50 p-8">
        <h1 class="text-2xl font-bold mb-6">Edit User</h1>
        <form action="{{ route('admin.users.update', $user->id) }}" method="POST" class="space-y-6 max-w-lg">
            @csrf
            @method('PUT')
            <div>
                <label class="block mb-1 font-semibold">Name</label>
                <input type="text" name="name" value="{{ old('name', $user->name) }}" class="w-full border rounded px-3 py-2" required>
            </div>
            <div>
                <label class="block mb-1 font-semibold">Email</label>
                <input type="email" name="email" value="{{ old('email', $user->email) }}" class="w-full border rounded px-3 py-2" required>
            </div>
            <div>
                <label class="block mb-1 font-semibold">Password</label>
                <input type="password" name="password" value="{{ old('password') }}" class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1 font-semibold">Confirm Password</label>
                <input type="password" name="password_confirmation" value="{{ old('password_confirmation') }}" class="w-full border rounded px-3 py-2">
            </div>
            <div>
                <label class="block mb-1 font-semibold">Admin</label>
                <select name="is_admin" class="w-full border rounded px-3 py-2">
                    <option value="0" {{ !$user->is_admin ? 'selected' : '' }}>No</option>
                    <option value="1" {{ $user->is_admin ? 'selected' : '' }}>Yes</option>
                </select>
            </div>
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded">Update User</button>
        </form>
    </main>
</div>
@endsection
