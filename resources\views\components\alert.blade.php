@props(['type' => 'info'])

@php
$classes = match($type) {
    'success' => 'bg-green-50 border-l-4 border-green-500 text-green-700',
    'error' => 'bg-red-50 border-l-4 border-red-500 text-red-700',
    'warning' => 'bg-yellow-50 border-l-4 border-yellow-500 text-yellow-700',
    'info' => 'bg-blue-50 border-l-4 border-blue-500 text-blue-700',
    default => 'bg-gray-50 border-l-4 border-gray-500 text-gray-700'
};
@endphp

<div {{ $attributes->merge(['class' => 'p-4 rounded-xl ' . $classes]) }} role="alert">
    <div class="flex items-center">
        @if($type === 'success')
            <i class="fas fa-check-circle mr-2"></i>
        @elseif($type === 'error')
            <i class="fas fa-exclamation-circle mr-2"></i>
        @elseif($type === 'warning')
            <i class="fas fa-exclamation-triangle mr-2"></i>
        @else
            <i class="fas fa-info-circle mr-2"></i>
        @endif
        <p>{{ $slot }}</p>
    </div>
</div>
