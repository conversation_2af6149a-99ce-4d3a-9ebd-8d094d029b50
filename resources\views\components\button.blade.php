@props(['type' => 'button', 'variant' => 'primary'])

@php
$classes = match($variant) {
    'primary' => 'bg-black text-white px-6 py-3 rounded-xl hover:bg-gray-800 transition-all duration-300 font-medium',
    'secondary' => 'bg-yellow-500 text-black px-6 py-3 rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium',
    'outline' => 'border border-white text-white px-6 py-3 rounded-xl hover:bg-white/10 transition-all duration-300 font-medium',
    default => 'bg-black text-white px-6 py-3 rounded-xl hover:bg-gray-800 transition-all duration-300 font-medium'
};
@endphp

<button {{ $attributes->merge(['type' => $type, 'class' => $classes]) }}>
    {{ $slot }}
</button>
