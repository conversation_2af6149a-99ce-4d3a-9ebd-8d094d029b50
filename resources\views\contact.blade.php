@extends('layouts.app')

@section('title', 'Contact Us')

@section('content')
<div class="bg-white py-16 px-4 sm:px-6 lg:px-8 min-h-[60vh]">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-4xl font-extrabold text-gray-900 mb-4 text-center">Contact Us</h1>
        <p class="mb-8 text-gray-500 text-center">Have questions or need help? Fill out the form below and our team will get back to you as soon as possible.</p>
        @if(session('success'))
            <div class="mt-6 bg-green-100 border border-green-200 text-green-800 px-4 py-3 rounded">
                {{ session('success') }}
            </div>
        @endif        
        <form method="POST" action="{{ route('contact.submit') }}" class="space-y-6 bg-gray-50 p-8 rounded-xl shadow">
            @csrf
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Your Name</label>
                <input type="text" name="name" id="name" required class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-yellow-500 focus:border-yellow-500">
            </div>
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Your Email</label>
                <input type="email" name="email" id="email" required class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-yellow-500 focus:border-yellow-500">
            </div>
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                <textarea name="message" id="message" rows="5" required class="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-yellow-500 focus:border-yellow-500"></textarea>
            </div>
            <div>
                <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-black bg-yellow-500 hover:bg-yellow-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all duration-300">Send Message</button>
            </div>
        </form>
    </div>
</div>
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl mx-auto text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Still Need Help?</h2>
            <p class="text-lg text-gray-600 mb-8">
                Our support team is available 24/7 to assist you with any questions or issues you may have.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <div class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-yellow-500 text-black mb-4">
                        <i class="fas fa-envelope text-xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
                    <p class="text-gray-600 mb-4">
                        Get a response within 24 hours
                    </p>
                    <a href="mailto:<EMAIL>" class="text-black hover:text-yellow-400 font-medium transition-colors duration-300">
                        <EMAIL>
                    </a>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <div class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-yellow-500 text-black mb-4">
                        <i class="fas fa-comments text-xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Live Chat</h3>
                    <p class="text-gray-600 mb-4">
                        Chat with our support team in real-time
                    </p>
                    <a href="javascript:void(0);" onclick="tidioChatApi.open()" class="text-black hover:text-yellow-400 font-medium transition-colors duration-300">
                        Start Chat
                    </a>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <div class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-yellow-500 text-black mb-4">
                        <i class="fas fa-phone-alt text-xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Phone Support</h3>
                    <p class="text-gray-600 mb-4">
                        Available 24/7 for urgent issues
                    </p>
                    <a href="tel:18887209320" class="text-black hover:text-yellow-400 font-medium transition-colors duration-300">
                        ******-720-9320
                    </a>
                </div>
            </div>                                
        </div>
    </div>
</section>
@endsection
