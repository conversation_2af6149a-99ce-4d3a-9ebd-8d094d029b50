@section('title', 'Affordable eSims for all Destinations - DataLendR')

@extends('layouts.app')

@section('content')
    <!-- Countries Header -->
    <section class="bg-black py-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
            <h1 class="text-4xl font-bold text-white mb-6 animate-fade-in">All Destinations</h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto animate-fade-in-delay">
                Browse our collection of eSIMs for over 190 countries worldwide. Find the perfect plan for your next adventure.
            </p>
        </div>
    </section>
    
    <!-- Countries Grid -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Search and Filter -->
            <div class="mb-12 bg-gray-50 p-6 rounded-2xl shadow-sm transition-all duration-300 hover:shadow-md">
                <form action="{{ route('search') }}" method="GET" class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="query" 
                               placeholder="Search destinations..." 
                               class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                               value="{{ request('query') }}">
                    </div>
                    <button type="submit" class="bg-black text-white px-6 py-3 rounded-xl hover:bg-gray-800 transition-all duration-300 flex items-center justify-center gap-2">
                        <i class="fas fa-search"></i> Search
                    </button>
                </form>
            </div>
            
            <!-- Sorting Toggle -->
            <div class="mb-8 flex flex-wrap gap-4 justify-center">
                <div class="bg-gray-50 p-2 rounded-xl inline-flex">
                    <button type="button" class="sort-btn active px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300" data-forcategory="local">
                        Local
                    </button>
                    <button type="button" class="sort-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300" data-forcategory="region">
                        Region
                    </button>
                    <button type="button" class="sort-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300" data-forcategory="global">
                        Global
                    </button>
                </div>
            </div>
            
            <!-- Countries Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($countries as $country)
                    <a href="{{ route('countries.show', $country->slug) }}" 
                       class="group bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-yellow-500/20" data-sortable @if($country->is_region) data-category="region" @elseif($country->is_global) data-category="global" @else data-category="local" @endif>
                        <div class="flex items-center gap-4">
                            <div class="relative">
                                <img src="{{ $country->flag_url }}" 
                                     alt="{{ $country->name }}" 
                                     class="w-12 h-12 rounded-full object-cover border border-gray-100 group-hover:border-yellow-500/50 transition-all duration-300">
                                <div class="absolute inset-0 bg-yellow-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-black transition-colors duration-300">
                                    {{ $country->name }}
                                </h3>
                                <p class="text-sm text-gray-600 mt-1">
                                    @if($country->price_from)
                                        From USD ${{ number_format($country->price_from, 2) }}
                                    @else
                                        View plans
                                    @endif
                                </p>
                            </div>
                            <div class="text-gray-400 group-hover:text-yellow-500 transition-colors duration-300">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-500">No countries available yet. Check back soon!</p>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
    
    <!-- FAQ Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Find answers to common questions about our international eSIM services.
                </p>
            </div>
            
            <div class="max-w-3xl mx-auto space-y-6">
                @php
                    $faqs = [
                        [
                            'question' => 'What is an eSIM?',
                            'answer' => 'An eSIM (embedded SIM) is a digital SIM that allows you to activate a cellular plan from your carrier without having to use a physical SIM card. It\'s built into your device and can be programmed to work with any compatible carrier.'
                        ],
                        [
                            'question' => 'Which devices support eSIM?',
                            'answer' => 'Many modern smartphones, tablets, and smartwatches support eSIM technology. This includes recent iPhone models (XR and newer), Google Pixel devices (2 and newer), Samsung Galaxy devices (S20 and newer), and many others.'
                        ],
                        [
                            'question' => 'How do I install an eSIM?',
                            'answer' => 'After purchasing an eSIM, you\'ll receive a QR code. On your device, go to Settings > Cellular/Mobile Data > Add Cellular/Mobile Plan, then scan the QR code. Follow the on-screen instructions to complete the installation.'
                        ],
                        [
                            'question' => 'Can I use my eSIM in multiple countries?',
                            'answer' => 'Yes, we offer regional and global eSIM plans that work in multiple countries. Check the plan details for coverage information. You can also purchase country-specific eSIMs for each destination if you prefer.'
                        ]
                    ];
                @endphp

                @foreach($faqs as $faq)
                    <div class="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $faq['question'] }}</h3>
                        <p class="text-gray-600 leading-relaxed">
                            {{ $faq['answer'] }}
                        </p>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
@endsection

@section('head')
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .animate-fade-in-delay {
            animation: fadeIn 0.6s ease-out 0.2s forwards;
            opacity: 0;
        }
        
        /* Sorting styles */
        .sort-btn {
            color: #6B7280;
        }
        .sort-btn.active {
            background: #000;
            color: #fff;
        }
        [data-sortable] {
            transition: all 0.3s ease;
        }
        [data-sortable].hidden {
            display: none;
        }
        [data-category="region"], [data-category="global"]{
            display: none;
        }
    </style>
@endsection
@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sortButtons = document.querySelectorAll('.sort-btn');
            const sortableItems = document.querySelectorAll('[data-sortable]');

            sortButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active state
                    sortButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    const category = this.dataset.forcategory;

                    // Filter items
                    sortableItems.forEach(item => {
                        if (category === 'all' || item.dataset.category === category) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
@endsection
