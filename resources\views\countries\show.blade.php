@extends('layouts.app')
@section('title', $country->name.' Affordable eSims')
@section('content')    
    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/4 mb-6 md:mb-0 flex justify-center">
                    <img src="{{ $country->flag_url }}" alt="{{ $country->name }}" class="h-32 w-48 object-cover rounded-xl shadow-lg">
                </div>
                <div class="md:w-3/4 md:pl-8 text-center md:text-left">
                    <h1 class="text-3xl font-bold text-white mb-2">{{ $country->name }}</h1>
                    <p class="text-xl text-gray-300 mb-4">
                        Stay connected in {{ $country->name }} with our affordable eSIM plans
                    </p>
                    <div class="flex flex-wrap gap-4 justify-center md:justify-start">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-500 text-black">
                            <i class="fas fa-wifi mr-2"></i> 5G/4G/LTE Coverage
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/10 text-white">
                            <i class="fas fa-bolt mr-2"></i> Instant Activation
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/10 text-white">
                            <i class="fas fa-check-circle mr-2"></i> 24/7 Support
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>
        
    <section class="py-8 bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="prose max-w-none">
                <p class="text-gray-600">
                    Stay connected during your trip to {{ $country->name }} with our reliable eSIM data plans. 
                    Enjoy seamless connectivity as soon as you land, with no need to search for local SIM cards or deal with language barriers.
                    Our eSIMs work with all major carriers in {{ $country->name }}, ensuring you have the best coverage wherever you go.
                </p>
            </div>
        </div>
    </section>
        
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 md:gap-5">
                <div class="col-span-1">
                    <img src="{{ url('images/'.$country->code.'.jpg') }}" alt="{{ $country->name }}" class="w-full rounded-xl mb-5 hidden md:block">
                    @if($country->description && $cd = json_decode($country->description))
                        <div class="text-center mt-8">
                            <button type="button" class="bg-yellow-500 text-black py-2 px-6 rounded-xl font-medium hover:bg-yellow-400 transition-all duration-300 w-full mb-8" onclick="openModal()">
                                <i class="fas fa-globe mr-2"></i> View Supported Countries
                            </button>
                        </div>

                        <!-- Modal -->
                        <div id="supportedCountriesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
                            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-xl bg-white">
                                <div class="flex justify-between items-center pb-3">
                                    <h3 class="text-xl font-bold text-gray-900">Supported Countries</h3>
                                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="mt-4">
                                    <div>
                                        @foreach($cd as $cc)
                                            <p class="text-gray-600 mb-3">
                                                <img src="{{ $cc->img_src}}" width="30" class="inline rounded mr-1"> {{ $cc->country }}
                                            </p>
                                        @endforeach
                                    </div>
                                </div>
                                <div class="mt-6 text-right">
                                    <button onclick="closeModal()" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-all duration-300">
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif 
                </div>
                <div class="col-span-2">
                    <h2 class="text-2xl font-bold text-gray-900 mb-8">Available eSIM Plans for {{ $country->name }}</h2>
                    
                    <!-- Plan Type Toggle -->
                    <div class="mb-8 flex flex-wrap gap-4">
                        <div class="bg-gray-50 p-2 rounded-xl inline-flex">
                            <button type="button" class="plan-btn active px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300" data-for-type="data-only">
                                Data Only
                            </button>
                            @php
                                $hasMinutesPlans = false;
                                foreach($esims as $esim) {
                                    if(strpos($esim->name, 'Mins') !== false) {
                                        $hasMinutesPlans = true;
                                        break;
                                    }
                                }
                            @endphp
                            @if($hasMinutesPlans)
                                <button type="button" class="plan-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300" data-for-type="data-minutes">
                                    Data + Minutes
                                </button>
                            @endif
                        </div>
                    </div>

                    @if(count($esims) > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($esims as $esim)
                                <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-200 hover:shadow-md transition-shadow duration-300" data-plan-type="{{ strpos($esim->name, 'Mins') ? 'data-minutes' : 'data-only' }}">
                                    <div class="p-4">
                                        <div class="mb-4">
                                            <p class="font-bold text-lg mb-1">{{ strpos($esim->data_amount, 'MB') ? $esim->data_amount : $esim->data_amount.' GB' }}</p>
                                            <p class="text-xs text-gray-700 mb-1">
                                            {{ $esim->name }}
                                            </p>    
                                            <!--
                                            @if($esim->features)
                                                <div class="mt-3">
                                                    <p class="text-sm font-medium text-gray-700 mb-1">Features:</p>
                                                    <ul class="text-sm text-gray-600 list-disc pl-5 space-y-1">
                                                        @foreach(json_decode($esim->description, true) as $feature)
                                                            <li>{{ $feature }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            @endif
                                            -->
                                        </div>
                            <div>
  <span class="text-lg font-bold text-black">
    ${{ number_format($esim->price(), 2) }}
  </span>
  <div class="flex items-center mt-1">
    <div class="inline-flex items-center bg-black text-white text-[8px] font-medium px-2 py-0.5 rounded-full" style="transform: scale(0.6); transform-origin: left;">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="white" viewBox="0 0 24 24">
        <path d="M12 1.75A10.25 10.25 0 1 0 22.25 12 10.262 10.262 0 0 0 12 1.75zm0 18.5A8.25 8.25 0 1 1 20.25 12 8.26 8.26 0 0 1 12 20.25zM11 6h2v6h-2zm0 8h2v2h-2z"/>
      </svg>
      15% OFF with HELLO15
    </div>
  </div>
</div>
                                        <div class="mt-3 pt-4">
                                            <form action="{{ route('esims.addToCart', $esim->id) }}" method="POST">
                                                @csrf
                                                <button type="submit" class="w-full bg-yellow-500 text-black py-2 px-4 rounded-xl font-medium hover:bg-yellow-200 transition-all duration-300">
                                                    <i class="fas fa-shopping-cart mr-2"></i> Buy Now
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="bg-gray-50 rounded-xl p-8 text-center border border-gray-100">
                            <i class="fas fa-info-circle text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No eSIM Plans Available Yet</h3>
                            <p class="text-gray-600 mb-4">
                                We're currently updating our eSIM plans for {{ $country->name }}. Please check back soon!
                            </p>
                            <a href="{{ route('countries.index') }}" class="bg-yellow-500 text-black py-3 px-6 rounded-xl font-medium hover:bg-yellow-400 transition-all duration-300 inline-block">
                                Browse Other Destinations
                            </a>
                        </div>
                    @endif                    
                </div>
            </div>              
        </div>
    </section>
    <section class="py-6 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

   <!-- Shop with confidence -->
<div class="bg-white rounded-lg p-4 mb-6 border border-gray-200">
  <h2 class="text-base font-semibold text-gray-900 mb-4">Shop your DataLendR eSIM with confidence.</h2>
  <ul class="space-y-2">
    <li class="flex items-center">
      <!-- Star rating icon -->
      <svg class="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.957a1 1 0 00.95.69h4.167c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.957c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.54-1.118l1.286-3.957a1 1 0 00-.364-1.118L2.04 9.384c-.783-.57-.38-1.81.588-1.81h4.167a1 1 0 00.95-.69l1.286-3.957z" />
      </svg>
      <span>4.9 (92,900+ reviews)</span>
    </li>
    <li class="flex items-center">
  <!-- Secure Checkout icon with check -->
      <svg class="h-5 w-5 text-black mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
        <!-- Small check inside shield -->
        <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>Secure Checkout</span>
    </li>
    <li class="flex items-center">
      <!-- Instant eSIM delivery icon -->
      <svg class="h-5 w-5 text-black mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path d="M3 12a9 9 0 1 0 9-9"/>
        <path d="M12 7v5l3 3"/>
      </svg>
      <span>Instant eSIM delivery</span>
    </li>
    <li class="flex items-center">
      <!-- 100% Refund Guarantee icon -->
      <svg class="h-5 w-5 text-black mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path d="M4.05 11a8 8 0 1 1 .45 4M4 19v-5h5"/>
      </svg>
      <span>100% Refund Guarantee</span>
    </li>
  </ul>
</div>


    <!-- Can I activate my plan later -->
    <div class="bg-white rounded-lg p-4 border border-gray-200">
      <h3 class="flex items-center text-base font-semibold text-gray-900 mb-2">
        <!-- Info icon -->
<svg class="h-5 w-5 text-black mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
  <path d="M12 16v-4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
  <circle cx="12" cy="8" r="1" fill="currentColor"/>
</svg>
        Can I activate my plan later?
      </h3>
      <p class="text-sm text-gray-900">
        Yes, absolutely—you can purchase and install your DataLendR eSIM well before your trip, and its validity typically won’t start until you’re connected in the destination country.
      </p>
    </div>

  </div>
</section>    
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Travel Tips for {{ $country->name }}</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-wifi text-black mr-2"></i> Connectivity Tips
                    </h3>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Activate your eSIM before you arrive in {{ $country->name }} to ensure immediate connectivity.</span>
                        </li>
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Most urban areas have excellent 4G coverage, but remote areas may have limited service.</span>
                        </li>
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Download maps and essential apps before your trip for offline access if needed.</span>
                        </li>
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Public Wi-Fi is available in many hotels, cafes, and restaurants as a backup option.</span>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-globe text-black mr-2"></i> General Travel Advice
                    </h3>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Check the local weather forecast before your trip to pack appropriately.</span>
                        </li>
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Familiarize yourself with local customs and basic phrases in the local language.</span>
                        </li>
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Keep digital copies of important documents like your passport and travel insurance.</span>
                        </li>
                        <li class="flex">
                            <i class="fas fa-check-circle text-black mt-1 mr-2"></i>
                            <span>Register with your country's embassy or consulate for emergency assistance if needed.</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>    
@endsection

@section('head')
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .animate-fade-in-delay {
            animation: fadeIn 0.6s ease-out 0.2s forwards;
            opacity: 0;
        }
        
        /* Sorting styles */
        .sort-btn {
            color: #6B7280;
        }
        .sort-btn.active {
            background: #000;
            color: #fff;
        }
        [data-sortable] {
            transition: all 0.3s ease;
        }
        [data-sortable].hidden {
            display: none;
        }
        [data-category="region"], [data-category="global"]{
            display: none;
        }

        /* Plan type toggle styles */
        .plan-btn {
            color: #6B7280;
        }
        .plan-btn.active {
            background: #000;
            color: #fff;
        }
        [data-plan-type] {
            transition: all 0.3s ease;
        }
        [data-plan-type=data-minutes]{
            display: none;
        }
    </style>
@endsection
@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const planButtons = document.querySelectorAll('.plan-btn');
            const planItems = document.querySelectorAll('[data-plan-type]');

            planButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active state
                    planButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    const planType = this.dataset.forType;

                    // Filter items
                    planItems.forEach(item => {
                        if (planType === 'all' || item.dataset.planType === planType) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        });

        function openModal() {
            document.getElementById('supportedCountriesModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('supportedCountriesModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('supportedCountriesModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
@endsection