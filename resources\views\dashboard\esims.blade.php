@extends('layouts.app')

@section('title', 'My eSIMs - DataLendR')

@section('content')
<div class="min-h-screen">
    <section class="bg-black py-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2 animate-fade-in">My eSIMs</h1>
                    <p class="text-xl text-gray-300 animate-fade-in-delay">Manage your active eSIMs</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('countries.index') }}" 
                       class="inline-flex items-center px-6 py-3 bg-yellow-500 text-black rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium">
                        <i class="fas fa-plus mr-2"></i> Buy New eSIM
                    </a>
                </div>
            </div>
        </div>
    </section>   

    <!-- Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Navigation -->
        <div class="mb-8">
            <nav class="flex space-x-4">
                <a href="{{ route('dashboard.index') }}" class="text-gray-600 hover:text-black px-3 py-2 rounded-md">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>
                <a href="{{ route('dashboard.esims') }}" class="bg-black text-white px-3 py-2 rounded-md shadow-sm font-medium">
                    <i class="fas fa-sim-card mr-2"></i> My eSIMs
                </a>
                <a href="{{ route('dashboard.settings') }}" class="text-gray-600 hover:text-black px-3 py-2 rounded-md">
                    <i class="fas fa-cog mr-2"></i> Settings
                </a>
            </nav>
        </div>
        
        <!-- Active eSIMs -->
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-100 mb-8">
            <div class="p-6 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-900">Active eSIMs</h2>
            </div>
            
            <div class="p-6">
                @if(count($activeEsims) > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($activeEsims as $order)
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <div class="p-4 bg-gray-50 border-b border-gray-200 flex items-center">
                                    <div class="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-md overflow-hidden mr-3">
                                        <img src="https://flagcdn.com/h40/{{ $order->esim->country->code }}.png" alt="{{ $order->esim->name }}" class="w-full h-full object-cover rounded-full">
                                    </div>
                                    <div>
                                        <h3 class="text-base font-medium text-gray-900">{{ $order->esim->name }}</h3>
                                        <p class="text-sm text-gray-500">{{ $order->esim->country->name }}</p>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="space-y-3 mb-4">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-500">Data</span>
                                            <span class="text-sm font-medium">{{ $order->esim->data_amount }}GB</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-500">Validity</span>
                                            <span class="text-sm font-medium">{{ $order->esim->validity_days }} days</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-500">Purchase Date</span>
                                            <span class="text-sm font-medium">{{ $order->created_at->format('M d, Y') }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-500">Status</span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-col space-y-2">
                                        <a href="{{ route('orders.show', $order->order_number) }}" class="text-black p-2 text-sm rounded-lg hover:bg-black hover:text-white">
                                            View QR Code
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="inline-flex items-center justify-center h-20 w-20 rounded-full bg-gray-100 text-gray-400 mb-6">
                            <i class="fas fa-sim-card text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-medium text-gray-900 mb-2">No Active eSIMs</h3>
                        <p class="text-gray-500 mb-6 max-w-md mx-auto">
                            You don't have any active eSIMs yet. Purchase your first eSIM to get started with global connectivity.
                        </p>
                        <a href="{{ route('countries.index') }}" class="bg-yellow-500 text-black text-sm p-2 rounded-lg hover:bg-yellow-400">
                            Browse eSIMs
                        </a>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- eSIM Usage Tips -->
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-100 mb-8">
        <div class="p-6 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-900">eSIM Usage Tips</h2>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gray-100 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <div class="p-2 rounded-full bg-yellow-100 text-yellow-600 mr-3">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <h3 class="text-base font-medium text-gray-900">Device Compatibility</h3>
                        </div>
                        <p class="text-sm text-gray-600">
                            Make sure your device is eSIM compatible. Most modern smartphones support eSIM technology, including iPhone XS or newer and many Android devices.
                        </p>
                    </div>
                    
                    <div class="bg-green-50 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <div class="p-2 rounded-full bg-green-100 text-green-600 mr-3">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h3 class="text-base font-medium text-gray-900">Activation Timing</h3>
                        </div>
                        <p class="text-sm text-gray-600">
                            Only activate your eSIM when you need it. The validity period starts from activation, not purchase date, so you can buy in advance.
                        </p>
                    </div>
                    
                    <div class="bg-purple-50 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <div class="p-2 rounded-full bg-purple-100 text-purple-600 mr-3">
                                <i class="fas fa-battery-three-quarters"></i>
                            </div>
                            <h3 class="text-base font-medium text-gray-900">Battery Saving</h3>
                        </div>
                        <p class="text-sm text-gray-600">
                            To save battery, disable your eSIM when not in use. Go to your device's mobile data settings and toggle off the eSIM line.
                        </p>
                    </div>
                </div>
                
                <div class="mt-6 text-center">
                    <a href="{{ route('help') }}" class="text-black hover:text-yellow-400 font-medium">
                        View More Tips <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection