@extends('layouts.app')

@section('title', 'Dashboard - DataLendR')

@section('content')
    <!-- Dashboard Header -->
    <section class="bg-black py-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2 animate-fade-in">Welcome back, {{ Auth::user()->name }}!</h1>
                    <p class="text-xl text-gray-300 animate-fade-in-delay">Here's an overview of your account</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('countries.index') }}" 
                       class="inline-flex items-center px-6 py-3 bg-yellow-500 text-black rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium">
                        <i class="fas fa-plus mr-2"></i> Buy New eSIM
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Content -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Active eSIMs -->
                <div class="bg-white rounded-2xl shadow-sm p-6 border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Active eSIMs</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">{{ count($activeEsims) }}</p>
                        </div>
                        <div class="w-12 h-12 rounded-xl bg-yellow-500/10 flex items-center justify-center">
                            <i class="fas fa-sim-card text-black text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Total Spent -->
                <div class="bg-white rounded-2xl shadow-sm p-6 border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Total Spent</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">${{ number_format($totalSpent, 2) }}</p>
                        </div>
                        <div class="w-12 h-12 rounded-xl bg-yellow-500/10 flex items-center justify-center">
                            <i class="fas fa-dollar-sign text-black text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-100 mb-8">
                <div class="p-6 border-b border-gray-100">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Orders</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100">
                            @forelse($recentOrders as $order)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        #{{ $order->id }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $order->created_at->format('M d, Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ${{ number_format($order->amount, 2) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-3 py-1 text-xs font-medium rounded-full 
                                            @if($order->payment_status === 'paid') bg-green-100 text-green-800
                                            @elseif($order->payment_status === 'pending') bg-yellow-100 text-yellow-800
                                            @else bg-red-100 text-red-800 @endif">
                                            {{ ucfirst($order->payment_status) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <a href="{{ route('orders.show', $order->order_number) }}" 
                                           class="text-black hover:text-yellow-600">View Details</a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                                        No orders found
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .animate-fade-in-delay {
            animation: fadeIn 0.6s ease-out 0.2s forwards;
            opacity: 0;
        }
    </style>
@endsection
