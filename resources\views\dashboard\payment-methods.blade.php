@extends('layouts.app')

@section('title', 'Payment Methods - DataLendR')

@section('content')
<div class="min-h-screen">
    <section class="bg-black py-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2 animate-fade-in">Payment Methods</h1>
                    <p class="text-xl text-gray-300 animate-fade-in-delay">Manage your payment information</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('countries.index') }}" 
                       class="inline-flex items-center px-6 py-3 bg-yellow-500 text-black rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium">
                        <i class="fas fa-plus mr-2"></i> Buy New eSIM
                    </a>
                </div>
            </div>
        </div>
    </section>   

    <!-- Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Navigation -->
        <div class="mb-8">
            <nav class="flex space-x-4">
                <a href="{{ route('dashboard.index') }}" class="text-gray-600 hover:text-yellow-500 px-3 py-2 rounded-md">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>
                <a href="{{ route('dashboard.esims') }}" class="text-gray-600 hover:text-yellow-500 px-3 py-2 rounded-md">
                    <i class="fas fa-sim-card mr-2"></i> My eSIMs
                </a>
                <a href="{{ route('dashboard.payment-methods') }}" class="bg-white text-black px-3 py-2 rounded-md shadow-sm font-medium">
                    <i class="fas fa-credit-card mr-2"></i> Payment Methods
                </a>
                <a href="{{ route('dashboard.settings') }}" class="text-gray-600 hover:text-yellow-500 px-3 py-2 rounded-md">
                    <i class="fas fa-cog mr-2"></i> Settings
                </a>
            </nav>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">
                
                <div class="bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden mb-8">
                    <div class="p-6 border-b border-gray-100 flex justify-between items-center">
                        <h2 class="text-xl font-bold text-gray-900 flex items-center gap-2"><svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M2 10h20" stroke="currentColor" stroke-width="2"></path></svg> Your Payment Methods</h2>
                        <button type="button" id="show-add-card" class="inline-flex items-center gap-1 text-sm bg-gradient-to-r from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-500 text-black font-semibold px-4 py-2 rounded-lg shadow transition">
                            <i class="fas fa-plus"></i> Add New
                        </button>
                    </div>
                    
                    <div class="p-6">
                        @forelse($paymentMethods as $pm)
                            <div class="border border-gray-200 rounded-lg p-4 mb-4 relative flex items-center justify-between bg-gray-100 hover:bg-gray-200 transition group">
                                <div class="flex items-center">
                                    <div class="text-yellow-600 text-2xl mr-4">
                                        <i class="fab fa-cc-{{ strtolower($pm->card->brand) }}"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 group-hover:text-yellow-500">{{ ucfirst($pm->card->brand) }} ending in {{ $pm->card->last4 }}</p>
                                        <p class="text-xs text-gray-500">Expires {{ $pm->card->exp_month }}/{{ $pm->card->exp_year }}</p>
                                    </div>
                                </div>
                                @if($pm->id === optional($user->stripe_default_payment_method)->id)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-800 border border-yellow-400">Default</span>
                                @endif
                            </div>
                        @empty
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700 p-4 rounded mb-4">
                                <p>No saved payment methods.</p>
                            </div>
                        @endforelse
                        
                        <div id="add-card-modal" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 hidden transition-opacity duration-200">
                            <div class="bg-white rounded-xl shadow-2xl p-8 w-full max-w-md relative animate-fade-in">
                                <button type="button" id="close-add-card" class="absolute top-2 right-2 text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                                <h3 class="text-lg font-bold mb-4 text-yellow-700 flex items-center gap-2"><svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M2 10h20" stroke="currentColor" stroke-width="2"></path></svg> Add New Card</h3>
                                <form id="add-card-form" method="POST" action="{{ route('dashboard.payment-methods.add') }}">
                                    @csrf
                                    <div id="card-element" class="mb-4"></div>
                                    <div id="card-errors" class="text-red-500 text-sm mb-4"></div>
                                    <button type="submit" class="w-full bg-gradient-to-r from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-500 text-black font-semibold py-3 px-4 rounded-lg shadow-lg transition flex items-center justify-center gap-2">
                                        <i class="fas fa-credit-card"></i> Add Card
                                    </button>
                                </form>
                            </div>
                        </div>
                        <script>
                            document.getElementById('show-add-card').addEventListener('click', function() {
                                document.getElementById('add-card-modal').classList.remove('hidden');
                            });
                            document.getElementById('close-add-card').addEventListener('click', function() {
                                document.getElementById('add-card-modal').classList.add('hidden');
                            });
                        </script>
                    </div>
                </div>
                
                <!-- Billing History -->
                <div class="bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden mb-8">
                    <div class="p-6 border-b border-gray-100 flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">Billing History</h2>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order #</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($orders as $order)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $order->created_at->format('M d, Y') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $order->order_number }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">${{ number_format($order->amount, 2) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($order->payment_status === 'paid') bg-green-100 text-green-800
                                                @elseif($order->payment_status === 'pending') bg-yellow-100 text-yellow-800
                                                @else bg-red-100 text-red-800 @endif">
                                                {{ ucfirst($order->payment_status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <a href="#" class="text-yellow-500 hover:underline"><i class="fas fa-download"></i></a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">No billing history found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Payment Security -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Security</h3>
                    
                    <div class="space-y-4 text-sm text-gray-600">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mt-0.5">
                                <i class="fas fa-lock text-green-500"></i>
                            </div>
                            <div class="ml-3">
                                <p>Your payment information is encrypted and securely stored by Stripe, our payment processor.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mt-0.5">
                                <i class="fas fa-shield-alt text-green-500"></i>
                            </div>
                            <div class="ml-3">
                                <p>We never store your full card details on our servers.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mt-0.5">
                                <i class="fas fa-check-circle text-green-500"></i>
                            </div>
                            <div class="ml-3">
                                <p>All transactions are protected with industry-standard SSL encryption.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- FAQ -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment FAQs</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-medium text-gray-900">What payment methods do you accept?</h4>
                            <p class="text-sm text-gray-600 mt-1">
                                We accept all major credit and debit cards, including Visa, Mastercard, American Express, and Discover.
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="font-medium text-gray-900">Are there any additional fees?</h4>
                            <p class="text-sm text-gray-600 mt-1">
                                No, the price you see is the price you pay. We don't add any hidden fees or charges.
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="font-medium text-gray-900">What currency will I be charged in?</h4>
                            <p class="text-sm text-gray-600 mt-1">
                                All charges are processed in US Dollars (USD). Your bank may apply currency conversion fees if your card is issued in a different currency.
                            </p>
                        </div>
                        
                        <div>
                            <h4 class="font-medium text-gray-900">How do refunds work?</h4>
                            <p class="text-sm text-gray-600 mt-1">
                                Refunds are processed back to the original payment method. Unused eSIMs may be eligible for a refund within 24 hours of purchase.
                            </p>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <a href="{{ route('help') }}" class="text-yellow-500 hover:text-yellow-400 font-medium text-sm">
                            View all payment FAQs <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const stripe = Stripe(@json(config('services.stripe.key')));
        const elements = stripe.elements();
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');

        const form = document.getElementById('add-card-form');
        if (form) {
            form.addEventListener('submit', async function (e) {
                e.preventDefault();
                const { setupIntent, error } = await stripe.confirmCardSetup(
                    @json($setupIntent->client_secret), {
                        payment_method: {
                            card: cardElement,
                        }
                    }
                );
                if (error) {
                    document.getElementById('card-errors').textContent = error.message;
                } else {
                    // Send payment_method.id to server
                    fetch(@json(route('dashboard.payment-methods.add')), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        },
                        body: JSON.stringify({ payment_method_id: setupIntent.payment_method })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            document.getElementById('card-errors').textContent = data.error || 'Failed to add card.';
                        }
                    });
                }
            });
        }
    });
</script>
@endpush