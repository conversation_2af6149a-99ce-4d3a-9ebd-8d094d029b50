@extends('layouts.app')

@section('title', 'Account Settings - DataLendR')

@section('content')
<div class="min-h-screen">
    <section class="bg-black py-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2 animate-fade-in">Account Settings</h1>
                    <p class="text-xl text-gray-300 animate-fade-in-delay">Manage your personal information</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('countries.index') }}" 
                       class="inline-flex items-center px-6 py-3 bg-yellow-500 text-black rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium">
                        <i class="fas fa-plus mr-2"></i> Buy New eSIM
                    </a>
                </div>
            </div>
        </div>
    </section>   
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">        
        <div class="mb-8">
            <nav class="flex space-x-4">
                <a href="{{ route('dashboard.index') }}" class="text-gray-600 hover:text-black px-3 py-2 rounded-md">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>
                <a href="{{ route('dashboard.esims') }}" class="text-gray-600 hover:text-black px-3 py-2 rounded-md">
                    <i class="fas fa-sim-card mr-2"></i> My eSIMs
                </a>
                <a href="{{ route('dashboard.settings') }}" class="bg-white text-black px-3 py-2 rounded-md shadow-sm font-medium">
                    <i class="fas fa-cog mr-2"></i> Settings
                </a>
            </nav>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">                
                <div class="bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden mb-8">
                    <div class="p-6 border-b border-gray-100 flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">Personal Information</h2>
                    </div>
                    
                    <div class="p-6">
                        @if(session('success'))
                            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded" role="alert">
                                <p>{{ session('success') }}</p>
                            </div>
                        @endif
                        
                        <form action="{{ route('dashboard.settings.update') }}" method="POST">
                            @csrf
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                    <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500 @error('name') border-red-500 @enderror">
                                    @error('name')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                                
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                    <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500 @error('email') border-red-500 @enderror">
                                    @error('email')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-200 pt-6 mt-6">
                                <button type="submit" class="bg-yellow-500 text-black font-medium py-2 px-4 rounded-xl hover:bg-yellow-400">
                                    Update Personal Information
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
                        
            <div class="lg:col-span-1">                
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 mr-4">
                            <i class="fas fa-user text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">{{ $user->name }}</h3>
                            <p class="text-sm text-gray-500">{{ $user->email }}</p>
                            <p class="text-xs text-gray-500 mt-1">Member since {{ $user->created_at->format('F Y') }}</p>
                        </div>
</div>

<div class="border-t border-gray-200 pt-4 mt-4">
    <div class="flex justify-between items-center py-2">
        <span class="text-sm text-gray-500">Account Status</span>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Active
        </span>
    </div>
</div>
</div>

<div class="bg-white rounded-lg shadow-sm p-6 mb-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Account Security</h3>
    
    <div class="space-y-4 text-sm text-gray-600">
        <div class="flex items-start">
            <div class="flex-shrink-0 mt-0.5">
                <i class="fas fa-lock text-green-500"></i>
            </div>
            <div class="ml-3">
                <p>We recommend using a strong, unique password that you don't use for other websites.</p>
            </div>
        </div>
        
        <div class="flex items-start">
            <div class="flex-shrink-0 mt-0.5">
                <i class="fas fa-shield-alt text-green-500"></i>
            </div>
            <div class="ml-3">
                <p>Your account is protected with secure password hashing and encryption.</p>
            </div>
        </div>
    </div>
</div>
</div>
</div>
</div>
</div>

@endsection