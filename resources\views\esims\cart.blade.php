@extends('layouts.app')

@section('content')
    <!-- <PERSON><PERSON> -->
    <section class="bg-black py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-3xl font-bold text-white mb-2">Your Shopping Cart</h1>
            <p class="text-xl text-gray-300">
                Review your selected eSIMs before checkout
            </p>
        </div>
    </section>
    
    <!-- Cart Content -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if(count($cart) > 0)
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Cart Items -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
                            <div class="p-6 bg-white border-b">
                                <h2 class="text-lg font-semibold text-gray-900">Cart Items ({{ count($cart) }})</h2>
                            </div>
                            
                            <div class="divide-y">
                                @foreach($cart as $id => $item)
                                    <div class="p-4 flex flex-col md:flex-row">
                                        <div class="flex-1">
                                            <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $item['name'] }}</h3>
                                            <p class="text-sm text-gray-600 mb-2">
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-black text-white">
                                                    {{ $item['country'] }}
                                                </span>
                                            </p>
                                            <div class="text-sm text-gray-600">
                                                <p><span class="font-medium">Data:</span> {{ $item['data_amount'] }}</p>
                                                <p><span class="font-medium">Validity:</span> {{ $item['validity_days'] }} days</p>
                                            </div>
                                        </div>
                                        
                                        <div class="flex flex-row md:flex-col items-center justify-between mt-4 md:mt-0 pt-4 md:pt-0 border-t md:border-t-0">
                                            <span class="text-lg font-bold text-black">${{ number_format($item['price'], 2) }}</span>
                                            <form action="{{ route('cart.remove', $id) }}" method="POST" class="mt-2">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-800 text-sm font-medium transition-colors duration-300">
                                                    <i class="fas fa-trash-alt mr-1"></i> Remove
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <a href="{{ route('countries.index') }}" class="text-black hover:text-yellow-400 font-medium transition-colors duration-300">
                                <i class="fas fa-arrow-left mr-1"></i> Continue Shopping
                            </a>
                        </div>
                    </div>
                    
                    <!-- Order Summary -->
                    <div>
                        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 sticky top-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                            
                            <div class="space-y-3 mb-6">
                                @php
                                    $subtotal = 0;
                                    foreach($cart as $item) {
                                        $subtotal += $item['price'];
                                    }
                                    
                                    // For demonstration purposes, let's assume there's no tax
                                    $tax = 0;
                                    $total = $subtotal + $tax;
                                    $showDiscount = isset($discount) && $discount > 0;
                                    $grandTotal = $total - $discount;
                                @endphp
                                <div class="flex justify-between items-center">
                                    <span class="font-medium">Subtotal</span>
                                    <span class="font-medium">${{ number_format($total, 2) }}</span>
                                </div>
                                @if($showDiscount)
                                <div class="flex justify-between items-center text-green-700">
                                    <span class="font-medium">Discount ({{ $coupon['code'] ?? '' }})</span>
                                    <span class="font-medium">- ${{ number_format($discount, 2) }}</span>
                                </div>
                                @endif
                                <div class="flex justify-between items-center">
                                    <span class="font-medium">Tax</span>
                                    <span class="font-medium">${{ number_format($tax, 2) }}</span>
                                </div>
                            </div>
                            <div class="border-t py-4 mb-6">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-900 font-semibold">Total:</span>
                                    <span class="text-2xl font-bold text-black">${{ number_format($grandTotal, 2) }}</span>
                                </div>
                            </div>
                            <!-- Discount Code -->
                            <div class="mb-6">
                                <label for="discount_code" class="block text-sm font-medium text-gray-700 mb-2">Discount Code</label>
                                <div class="flex relative">
                                    <input type="text" id="discount_code" name="discount_code" placeholder="Enter code" class="input-field rounded-lg border border-gray-100 pl-2 w-full py-3" value="{{ session('coupon.code') ?? '' }}">
                                    <button id="apply-coupon-btn" type="button" class="bg-yellow-500 text-black px-4 py-2 rounded-lg hover:bg-yellow-400 transition-colors duration-300 text-sm absolute right-2 top-2 {{ session('coupon') ? 'hidden' : '' }}">
                                        Apply
                                    </button>
                                    <button id="remove-coupon-btn" type="button" class="text-sm ml-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors duration-300 {{ session('coupon') ? '' : 'hidden' }} absolute right-2 top-2">
                                        X
                                    </button>
                                </div>
                                <p id="coupon-message" class="text-green-600 text-sm mt-1 {{ session('coupon') ? '' : 'hidden' }}">
                                    <i class="fas fa-check-circle mr-1"></i> <span id="coupon-success-msg">{{ session('coupon.discount_percent') ? session('coupon.discount_percent').'% discount applied!' : '' }}</span>
                                </p>
                                <p id="coupon-error" class="text-red-600 text-sm mt-1 hidden"></p>
                            </div>
                            <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const applyBtn = document.getElementById('apply-coupon-btn');
                                const removeBtn = document.getElementById('remove-coupon-btn');
                                const codeInput = document.getElementById('discount_code');
                                const msg = document.getElementById('coupon-message');
                                const errorMsg = document.getElementById('coupon-error');
                                const successMsg = document.getElementById('coupon-success-msg');

                                applyBtn.addEventListener('click', function() {
                                    fetch("{{ route('cart.coupon.apply') }}", {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                        },
                                        body: JSON.stringify({ code: codeInput.value })
                                    })
                                    .then(res => res.json())
                                    .then(data => {
                                        if(data.success) {
                                            msg.classList.remove('hidden');
                                            errorMsg.classList.add('hidden');
                                            successMsg.textContent = data.discount_percent + '% discount applied!';
                                            removeBtn.classList.remove('hidden');
                                            // Optionally reload to update totals
                                            location.reload();
                                        } else {
                                            msg.classList.add('hidden');
                                            errorMsg.textContent = data.message;
                                            errorMsg.classList.remove('hidden');
                                        }
                                    });
                                });
                                if(removeBtn) {
                                    removeBtn.addEventListener('click', function() {
                                        fetch("{{ route('cart.coupon.remove') }}", {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                            }
                                        })
                                        .then(res => res.json())
                                        .then(data => {
                                            if(data.success) {
                                                msg.classList.add('hidden');
                                                errorMsg.classList.add('hidden');
                                                codeInput.value = '';
                                                removeBtn.classList.add('hidden');
                                                // Optionally reload to update totals
                                                location.reload();
                                            }
                                        });
                                    });
                                }
                            });
                            </script>
                            <a href="{{ route('checkout') }}" class="w-full bg-yellow-500 text-black py-3 px-4 rounded-xl font-medium hover:bg-yellow-400 transition-all duration-300 text-center block">
                                Proceed to Checkout
                            </a>
                            
                            <div class="mt-6 text-center text-sm text-gray-600">
                                <p class="flex items-center justify-center">
                                    <i class="fas fa-lock mr-2 text-gray-500"></i> Secure checkout
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="bg-white rounded-xl shadow-sm p-8 text-center border border-gray-100">
                    <div class="inline-flex items-center justify-center h-24 w-24 rounded-full bg-gray-100 text-gray-400 mb-6">
                        <i class="fas fa-shopping-cart text-4xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Your Cart is Empty</h2>
                    <p class="text-gray-600 mb-6 max-w-md mx-auto">
                        Looks like you haven't added any eSIMs to your cart yet. Browse our destinations to find the perfect eSIM for your next adventure.
                    </p>
                    <a href="{{ route('countries.index') }}" class="bg-yellow-500 text-black py-3 px-6 rounded-xl font-medium hover:bg-yellow-400 transition-all duration-300 inline-block">
                        Browse Destinations
                    </a>
                </div>
            @endif
        </div>
    </section>
@endsection
