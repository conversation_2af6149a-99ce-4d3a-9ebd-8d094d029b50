@extends('layouts.app')

@section('content')

    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/4 mb-6 md:mb-0 flex justify-center">
                    <img src="{{ $country->flag_url }}" alt="{{ $country->name }}" class="h-32 w-48 object-cover rounded-xl shadow-lg">
                </div>
                <div class="md:w-3/4 md:pl-8 text-center md:text-left">
                    <div class="flex items-center mb-2">
                        <h1 class="text-3xl font-bold text-white">{{ $esim->name }}</h1>
                    </div>
                    <p class="text-xl text-gray-300 mb-4">
                        {{ $country->name }} eSIM Plan
                    </p>
                    <div class="flex flex-wrap gap-4 justify-center md:justify-start">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-500 text-black">
                            <i class="fas fa-database mr-2"></i> {{ strpos($esim->data_amount, 'MB') ? $esim->data_amount : $esim->data_amount.' GB' }}
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/10 text-white">
                            <i class="far fa-clock mr-2"></i> {{ $esim->validity_days }} days
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/10 text-white">
                            <i class="fas fa-tag mr-2"></i> ${{ number_format($esim->price(), 2) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Plan Details</h2>
                        
                        <div class="prose max-w-none">
                            <p class="text-gray-600">
                                Stay connected in {{ $country->name }} with our reliable {{ $esim->name }} eSIM plan. 
                                Enjoy {{ $esim->data_amount }} of high-speed data valid for {{ $esim->validity_days }} days.
                                This plan works with all major carriers in {{ $country->name }}, ensuring you have the best coverage wherever you go.
                            </p>
                        </div>
                        
                        <div class="mt-6 border-t pt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Plan Features</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Included:</h4>
                                    <ul class="space-y-2">
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-yellow-500 mt-1 mr-2"></i>
                                            <span class="text-gray-600">{{ $esim->data_amount }}GB of high-speed data</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-yellow-500 mt-1 mr-2"></i>
                                            <span class="text-gray-600">{{ $esim->validity_days }} days validity</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-yellow-500 mt-1 mr-2"></i>
                                            <span class="text-gray-600">Easy installation via QR code</span>
                                        </li>
                                    </ul>
                                </div>
                                
                                @if($esim->description && $features = json_decode($esim->description))
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Additional Features:</h4>
                                        <ul class="space-y-2">
                                            @foreach($features as $feature)
                                                <li class="flex items-start">
                                                    <i class="fas fa-check-circle text-yellow-500 mt-1 mr-2"></i>
                                                    <span class="text-gray-600">{{ $feature }}</span>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">How to Install Your eSIM</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 text-black text-2xl font-bold mb-4">1</div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Purchase</h3>
                                <p class="text-gray-600">
                                    Buy your eSIM online and complete the checkout process.
                                </p>
                            </div>
                            
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 text-black text-2xl font-bold mb-4">2</div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Scan QR Code</h3>
                                <p class="text-gray-600">
                                    Scan the QR code you receive to install the eSIM on your device.
                                </p>
                            </div>
                            
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 text-black text-2xl font-bold mb-4">3</div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Activate & Connect</h3>
                                <p class="text-gray-600">
                                    Activate your eSIM when you arrive at your destination and enjoy!
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100 sticky top-6">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $esim->name }}</h2>
                        
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Data:</span>
                                <span class="font-medium">{{ $esim->data_amount }}GB</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Validity:</span>
                                <span class="font-medium">{{ $esim->validity_days }} days</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Coverage:</span>
                                <span class="font-medium">{{ $country->name }}</span>
                            </div>
                        </div>
                        
                        <div class="border-t border-b py-4 my-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-800 font-medium">Price:</span>
                                <span class="text-2xl font-bold text-yellow-500">${{ number_format($esim->price(), 2) }}</span>
                            </div>
                        </div>
                        
                        <form action="{{ route('esims.addToCart', $esim->id) }}" method="POST" class="mt-6">
                            @csrf
                            <button type="submit" class="w-full bg-yellow-500 text-black py-3 px-4 rounded-xl font-medium hover:bg-yellow-400 transition-all duration-300 text-center">
                                <i class="fas fa-shopping-cart mr-2"></i> Add to Cart
                            </button>
                        </form>
                        
                        <div class="mt-6 text-center">
                            <a href="{{ route('countries.show', $country->slug) }}" class="text-yellow-500 hover:text-yellow-400 text-sm font-medium transition-colors duration-300">
                                <i class="fas fa-arrow-left mr-1"></i> View other plans for {{ $country->name }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
        
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Compatible Devices</h2>
            
            <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <p class="text-gray-600 mb-6">
                    Our eSIMs are compatible with a wide range of devices. Make sure your device supports eSIM technology before purchasing.
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Apple Devices</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                iPhone XS, XS Max, XR and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                iPad Pro 11-inch (1st generation) and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                iPad Pro 12.9-inch (3rd generation) and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                iPad Air (3rd generation) and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                iPad (7th generation) and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                iPad mini (5th generation) and newer
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Android Devices</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Google Pixel 2 and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Samsung Galaxy S20 and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Samsung Galaxy Note 20 and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Samsung Galaxy Fold and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Samsung Galaxy Z Flip and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Motorola Razr (2019) and newer
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Huawei P40 and newer
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Other Devices</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Microsoft Surface Pro X
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Lenovo Yoga 5G
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Dell Latitude 9510
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                HP Elite Dragonfly G2
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                Acer Swift 3
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-yellow-500 mr-2"></i>
                                And many more...
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-6 pt-6 border-t text-center">
                    <p class="text-gray-600">
                        Not sure if your device is compatible? <a href="{{ route('help') }}" class="text-yellow-500 hover:text-yellow-400 font-medium transition-colors duration-300">Contact our support team</a> for assistance.
                    </p>
                </div>
            </div>
        </div>
    </section>
@endsection
