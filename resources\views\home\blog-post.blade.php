@extends('layouts.app')

@section('content')
    <!-- Blog Post Header -->
    <section class="bg-black py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold text-white mb-4">{{ $post->title }}</h1>
            <div class="flex items-center justify-center text-gray-300">
                <span class="text-sm">{{ $post->created_at->format('F j, Y') }}</span>
            </div>
        </div>
    </section>

    <!-- Blog Post Content -->
    <section class="py-12 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="prose prose-lg max-w-none">
                @if($post->image)
                <div class="mb-8 rounded-xl overflow-hidden">
                    <img src="{{ $post->image }}" alt="{{ $post->title }}" class="w-full h-auto">
                </div>
                @endif
                
                <div class="blog-content">
                    {!! $post->content !!}
                </div>
            </div>

            <!-- Share Buttons -->
            <div class="mt-12 pt-8 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Share this article</h3>
                <div class="flex space-x-4">
                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($post->title) }}" 
                       class="text-gray-600 hover:text-blue-500 transition-colors duration-300" target="_blank">
                        <i class="fab fa-x-twitter text-xl"></i>
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                       class="text-gray-600 hover:text-blue-600 transition-colors duration-300" target="_blank">
                        <i class="fab fa-facebook text-xl"></i>
                    </a>
                    <a href="https://www.linkedin.com/shareArticle?mini=true&url={{ urlencode(request()->url()) }}&title={{ urlencode($post->title) }}" 
                       class="text-gray-600 hover:text-blue-700 transition-colors duration-300" target="_blank">
                        <i class="fab fa-linkedin text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Posts -->
    @if($relatedPosts->count() > 0)
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Articles</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @foreach($relatedPosts as $relatedPost)
                <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
                    @if($relatedPost->image)
                    <div class="aspect-w-16 aspect-h-9">
                        <img src="{{ $relatedPost->image }}" alt="{{ $relatedPost->title }}" class="w-full h-full object-cover">
                    </div>
                    @endif
                    <div class="p-6">
                        <div class="flex items-center mb-2">
                            <span class="text-xs text-gray-500">{{ $relatedPost->created_at->format('F j, Y') }}</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $relatedPost->title }}</h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">
                            {{ \Illuminate\Support\Str::limit(strip_tags($relatedPost->content), 150) }}
                        </p>
                        <a href="{{ route('blog.post', $relatedPost->slug) }}" class="text-gray-500 hover:text-gray-400 font-medium inline-flex items-center transition-colors duration-300">
                            Read More <i class="fas fa-arrow-right ml-1 text-sm"></i>
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

   @endsection 
@section('head')
    <style>
        .blog-content h2{
            font-size: 20px;
            font-weight: 700;
            margin: 20px 0;
            opacity: 0.8;
        }
        .blog-content p{
            margin-bottom: 10px;
        }
    </style>
@endsection