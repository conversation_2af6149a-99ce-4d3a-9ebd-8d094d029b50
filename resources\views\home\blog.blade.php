@extends('layouts.app')
@section('title', 'Blog - DataLendR')
@section('content')
    <!-- Blog Header -->
    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-3xl font-bold text-white mb-4">Travel Blog</h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Tips, guides, and inspiration for your next adventure.
            </p>
        </div>
    </section>

    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Recent Articles</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($posts as $post)
                <div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
                    <div class="aspect-w-16 aspect-h-9">
                        <img src="{{ $post->image }}" alt="{{ $post->title }}" class="w-full h-full object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-2">
                            <span class="ml-2 text-xs text-gray-500">{{ $post->created_at->format('F j, Y') }}</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $post->title }}</h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">
                            {{ $post->content }}
                        </p>
                        <a href="{{ route('blog.post', $post->slug) }}" class="text-grey-600 hover:text-grey-400 font-medium inline-flex items-center transition-colors duration-300">
                            Read More <i class="fas fa-arrow-right ml-1 text-sm"></i>
                        </a>
                    </div>
                </div>
                @endforeach               
            </div>
            
            <!-- Pagination -->
            <div class="mt-12 flex justify-center hidden">
                <nav class="inline-flex rounded-xl shadow">
                    <a href="#" class="inline-flex items-center px-4 py-2 rounded-l-xl border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-300">
                        <i class="fas fa-chevron-left mr-2"></i> Previous
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-yellow-600">
                        1
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-300">
                        2
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-300">
                        3
                    </a>
                    <span class="inline-flex items-center px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-500">
                        ...
                    </span>
                    <a href="#" class="inline-flex items-center px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-300">
                        8
                    </a>
                    <a href="#" class="inline-flex items-center px-4 py-2 rounded-r-xl border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-300">
                        Next <i class="fas fa-chevron-right ml-2"></i>
                    </a>
                </nav>
            </div>
        </div>
    </section>
@endsection