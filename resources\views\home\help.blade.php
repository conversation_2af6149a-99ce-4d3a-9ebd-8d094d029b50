@extends('layouts.app')
@section('title', 'Help Center - DataLendR')
@section('content')
    <!-- Help Header -->
    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-3xl font-bold text-white mb-4">Help Center</h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Find answers to common questions and get support for your eSIM needs.
            </p>
        </div>
    </section>
    
    <!-- Search Section -->
    <section class="py-12 bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-3xl mx-auto">
                
                <div class="text-center">
                    <h2 class="text-lg font-medium text-gray-900 mb-2">Popular Topics</h2>
                    <div class="flex flex-wrap justify-center gap-2">
                        <a href="#installation" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-500 text-black hover:bg-yellow-400 transition-all duration-300">
                            eSIM Installation
                        </a>
                        <a href="#activation" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-all duration-300">
                            Activation
                        </a>
                        <a href="#compatibility" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-all duration-300">
                            Device Compatibility
                        </a>
                        <a href="#troubleshooting" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-all duration-300">
                            Troubleshooting
                        </a>
                        <a href="#billing" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-all duration-300">
                            Billing
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- FAQ Sections -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-3xl mx-auto">
                <!-- eSIM Basics -->
                <div class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">eSIM Basics</h2>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">What is an eSIM?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    An eSIM (embedded SIM) is a digital SIM that allows you to activate a cellular plan from your carrier without having to use a physical SIM card. It's built into your device and can be programmed to work with any compatible carrier.
                                </p>
                                <p>
                                    Unlike traditional physical SIM cards, eSIMs are embedded directly into your device's hardware, making it easier to switch between carriers or plans without needing to swap physical cards.
                                </p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">How does an eSIM work?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    An eSIM works by storing your carrier's profile information digitally on your device. When you purchase an eSIM from us, you'll receive a QR code that contains all the necessary information to set up your eSIM.
                                </p>
                                <p>
                                    By scanning this QR code in your device's settings, your device downloads and installs the eSIM profile, allowing you to connect to the mobile network in your destination country.
                                </p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">What are the benefits of using an eSIM?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <ul>
                                    <li><strong>Convenience:</strong> No need to find a local SIM card shop or deal with language barriers.</li>
                                    <li><strong>Instant Activation:</strong> Get connected immediately upon arrival at your destination.</li>
                                    <li><strong>Multiple Profiles:</strong> Store multiple eSIM profiles on one device.</li>
                                    <li><strong>Dual SIM Functionality:</strong> Use your regular SIM and eSIM simultaneously on compatible devices.</li>
                                    <li><strong>Environmentally Friendly:</strong> No plastic SIM cards needed.</li>
                                    <li><strong>Security:</strong> eSIMs are more secure and cannot be physically removed from your device.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Installation & Activation -->
                <div class="mb-12" id="installation">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Installation & Activation</h2>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">How do I install my eSIM on an iPhone?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <ol>
                                    <li>Go to <strong>Settings</strong> on your iPhone.</li>
                                    <li>Tap <strong>Cellular</strong> or <strong>Mobile Data</strong>.</li>
                                    <li>Tap <strong>Add Cellular Plan</strong> or <strong>Add Mobile Data Plan</strong>.</li>
                                    <li>Scan the QR code provided after your purchase.</li>
                                    <li>Follow the on-screen instructions to complete the installation.</li>
                                    <li>When prompted, select <strong>Add Cellular Plan</strong>.</li>
                                    <li>Choose a label for your new plan, such as "Travel" or the country name.</li>
                                    <li>Select your default line for calls, messages, and data.</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">How do I install my eSIM on an Android device?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <ol>
                                    <li>Go to <strong>Settings</strong> on your Android device.</li>
                                    <li>Tap <strong>Network & Internet</strong> or <strong>Connections</strong>.</li>
                                    <li>Tap <strong>Mobile Network</strong> or <strong>SIM Manager</strong>.</li>
                                    <li>Tap <strong>Add Mobile Plan</strong> or <strong>+</strong> icon.</li>
                                    <li>Tap <strong>Download a New SIM Instead</strong> or <strong>Add Using QR Code</strong>.</li>
                                    <li>Scan the QR code provided after your purchase.</li>
                                    <li>Follow the on-screen instructions to complete the installation.</li>
                                    <li>When prompted, enter the activation code provided.</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100" id="activation">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">When does my eSIM activate?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    Your eSIM will activate when you first connect to a network in your destination country. The validity period begins from the moment of activation, not from the purchase date.
                                </p>
                                <p>
                                    This means you can purchase your eSIM well in advance of your trip, and it will only start counting down once you arrive and connect to the local network.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Device Compatibility -->
                <div class="mb-12" id="compatibility">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Device Compatibility</h2>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Which devices support eSIM?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    Many modern smartphones, tablets, and smartwatches support eSIM technology. Here's a list of some compatible devices:
                                </p>
                                
                                <h4>Apple Devices:</h4>
                                <ul>
                                    <li>iPhone XS, XS Max, XR and newer</li>
                                    <li>iPad Pro 11-inch (1st generation) and newer</li>
                                    <li>iPad Pro 12.9-inch (3rd generation) and newer</li>
                                    <li>iPad Air (3rd generation) and newer</li>
                                    <li>iPad (7th generation) and newer</li>
                                    <li>iPad mini (5th generation) and newer</li>
                                    <li>Apple Watch Series 3 and newer (Cellular models)</li>
                                </ul>
                                
                                <h4>Android Devices:</h4>
                                <ul>
                                    <li>Google Pixel 2 and newer</li>
                                    <li>Samsung Galaxy S20 and newer</li>
                                    <li>Samsung Galaxy Note 20 and newer</li>
                                    <li>Samsung Galaxy Fold and newer</li>
                                    <li>Samsung Galaxy Z Flip and newer</li>
                                    <li>Motorola Razr (2019) and newer</li>
                                    <li>Huawei P40 and newer</li>
                                    <li>OnePlus 7 and newer</li>
                                    <li>Many other recent Android devices</li>
                                </ul>
                                
                                <p>
                                    This is not an exhaustive list. To check if your device supports eSIM, please consult your device manufacturer's specifications or contact our support team.
                                </p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">How do I check if my device is eSIM compatible?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    There are several ways to check if your device supports eSIM:
                                </p>
                                
                                <ol>
                                    <li>
                                        <strong>Check your device settings:</strong>
                                        <ul>
                                            <li>On iPhone: Go to Settings > Cellular/Mobile Data. If you see an option for "Add Cellular Plan," your device supports eSIM.</li>
                                            <li>On Android: Go to Settings > Network & Internet or Connections. If you see options related to eSIM or multiple SIMs, your device likely supports eSIM.</li>
                                        </ul>
                                    </li>
                                    <li>
                                        <strong>Check the manufacturer's website:</strong> Look up your device model on the manufacturer's website and check the specifications.
                                    </li>
                                    <li>
                                        <strong>Contact your device manufacturer:</strong> Reach out to customer support for your device manufacturer.
                                    </li>
                                    <li>
                                        <strong>Contact our support team:</strong> Our team can help you determine if your device is compatible with our eSIM service.
                                    </li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Troubleshooting -->
                <div class="mb-12" id="troubleshooting">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Troubleshooting</h2>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">I can't scan the QR code. What should I do?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    If you're having trouble scanning the QR code, try these solutions:
                                </p>
                                
                                <ol>
                                    <li>Make sure your device's camera is clean and free of any obstructions.</li>
                                    <li>Ensure you have good lighting conditions when scanning the QR code.</li>
                                    <li>Try taking a screenshot of the QR code and scanning it from your photo gallery.</li>
                                    <li>If you received the QR code via email, try opening it on a different device and scanning it from there.</li>
                                    <li>Check if your device has the latest operating system update.</li>
                                    <li>If you still can't scan the QR code, contact our support team for manual activation instructions.</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">My eSIM is installed but not connecting to the network. What should I do?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    If your eSIM is installed but not connecting to the network, try these troubleshooting steps:
                                </p>
                                
                                <ol>
                                    <li>Make sure you're in the country or region where your eSIM is valid.</li>
                                    <li>Check if data roaming is enabled for your eSIM:
                                        <ul>
                                            <li>On iPhone: Go to Settings > Cellular/Mobile Data > Cellular/Mobile Data Options > Data Roaming and turn it on.</li>
                                            <li>On Android: Go to Settings > Network & Internet > Mobile Network > Roaming and turn it on.</li>
                                        </ul>
                                    </li>
                                    <li>Restart your device.</li>
                                    <li>Toggle Airplane Mode on and off.</li>
                                    <li>Check if your eSIM is selected as the primary data line:
                                        <ul>
                                            <li>On iPhone: Go to Settings > Cellular/Mobile Data > Cellular/Mobile Data Options > Data Switching and select your eSIM.</li>
                                            <li>On Android: Go to Settings > Network & Internet > Mobile Network and select your eSIM for data.</li>
                                        </ul>
                                    </li>
                                    <li>If none of these solutions work, contact our support team for assistance.</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Billing & Payments -->
                <div class="mb-12" id="billing">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Billing & Payments</h2>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">What payment methods do you accept?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    We accept the following payment methods:
                                </p>
                                
                                <ul>
                                    <li>Credit and debit cards (Visa, Mastercard, American Express, Discover)</li>
                                    <li>PayPal</li>
                                    <li>Apple Pay (on compatible devices)</li>
                                    <li>Google Pay (on compatible devices)</li>
                                </ul>
                                
                                <p>
                                    All payments are processed securely through our payment provider, Stripe.
                                </p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Can I get a refund if I don't use my eSIM?</h3>
                            <div class="prose prose-sm max-w-none text-gray-600">
                                <p>
                                    Our refund policy is as follows:
                                </p>
                                
                                <ul>
                                    <li><strong>Unused eSIMs:</strong> If you haven't activated your eSIM (i.e., it hasn't connected to a network), you can request a refund within 30 days of purchase.</li>
                                    <li><strong>Activated eSIMs:</strong> Once an eSIM has been activated (connected to a network), it cannot be refunded.</li>
                                    <li><strong>Technical Issues:</strong> If you experience technical issues with your eSIM that our support team cannot resolve, you may be eligible for a refund or replacement at our discretion.</li>
                                </ul>
                                
                                <p>
                                    To request a refund, please contact our support team with your order number and reason for the refund.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Contact Support -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Still Need Help?</h2>
                <p class="text-lg text-gray-600 mb-8">
                    Our support team is available 24/7 to assist you with any questions or issues you may have.
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-yellow-500 text-black mb-4">
                            <i class="fas fa-envelope text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
                        <p class="text-gray-600 mb-4">
                            Get a response within 24 hours
                        </p>
                        <a href="mailto:<EMAIL>" class="text-black hover:text-yellow-400 font-medium transition-colors duration-300">
                            <EMAIL>
                        </a>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-yellow-500 text-black mb-4">
                            <i class="fas fa-comments text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Live Chat</h3>
                        <p class="text-gray-600 mb-4">
                            Chat with our support team in real-time
                        </p>
                        <a href="javascript:void(0);" onclick="tidioChatApi.open()" class="text-black hover:text-yellow-400 font-medium transition-colors duration-300">
                            Start Chat
                        </a>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-yellow-500 text-black mb-4">
                            <i class="fas fa-phone-alt text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Phone Support</h3>
                        <p class="text-gray-600 mb-4">
                            Available 24/7 for urgent issues
                        </p>
                        <a href="tel:18887209320" class="text-black hover:text-yellow-400 font-medium transition-colors duration-300">
                            ******-720-9320
                        </a>
                    </div>
                </div>                                
            </div>
        </div>
    </section>
@endsection
