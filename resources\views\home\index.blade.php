@extends('layouts.app')
@section('head')
    <style>
        .autoComplete_wrapper {
            position: relative;
        }

        ul#autoComplete_list_1 {
            position: absolute;
            z-index: 9999;
            background: #fff;
            width: 100%;
            padding: 10px !important;
            border-radius: 0 0 10px 10px;
            margin-top: -10px;
            --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        }
        .owl-carousel .owl-item img{
            width: auto !important;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css" integrity="sha512-tS3S5qG0BlhnQROyJXvNjeEM4UpMXHrQfTGmbQ1gKmelCxlSEBUaxhRBj/EFTzpbP4RVSrpEikbmdJobCvhE3g==" crossorigin="anonymous" referrerpolicy="no-referrer" />
@endsection
@section('scripts')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js" integrity="sha512-bPs7Ae6pVvhOSiIcyUClR7/q2OAsRiovw4vAkX+zJbw3ShAeeqezq50RIIcIURq7Oa20rW2n2q+fyXBNcU9lrw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script>
        $(document).ready(function(){
            $(".owl-carousel").owlCarousel({
                loop:true,
                items: 1,
                autoplay:true,
                autoplayTimeout:3000,
                autoplayHoverPause:true
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@tarekraafat/autocomplete.js@10.2.9/dist/autoComplete.min.js"></script>
    <script>
        const flags = @json($countries->pluck('flag_url', 'name'));
        const countrySlugs = @json($countries->pluck('slug', 'name'));

        const autoCompleteJS = new autoComplete({
            selector: "#autoComplete",
            placeHolder: "Where are you traveling to?",
            data: {
                src: @json($countries->pluck('name')),
                cache: true,
            },
            resultsList: {
                element: (list, data) => {
                    if (!data.results.length) {                        
                        const message = document.createElement("div");                        
                        message.setAttribute("class", "no_result");                        
                        message.innerHTML = `<span>Found No Results for "${data.query}"</span>`;                        
                        list.prepend(message);
                    }
                },
                noResults: true,
            },
            resultItem: {
                class: "flex mb-3 cursor-pointer hover:bg-yellow-500 rounded p-2",
                element: (item, data) => {
                    item.innerHTML = `
                    <span class="w-10 mr-3">
                        <img src="${flags[data.match]}" class="w-100 rounded-full">
                    </span>
                    <span>
                        ${data.match}
                    </span>`;
                },
                highlight: false,
            }
        });
        autoCompleteJS.input.addEventListener("selection", function (event) {
            const feedback = event.detail;
            autoCompleteJS.input.blur();
            
            const selection = feedback.selection.value;
            
            const slug = countrySlugs[selection];
            if (slug) {
                window.location.href = "{{ url('countries') }}/" + slug;
            }
        });
    </script>
@endsection
@section('content')    
    <section class="relative bg-yellow-500 pt-24" style="background-image: url('image2.jpg'); background-size: cover; background-position: center;">        
        <div class="flex-none md:flex max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-xl animate-fade-in relative z-40">
                <h3 class="text-2xl font-bold text-black mb-4">REDEFINING DATA ROAMING.</h3>
                <h1 class="text-4xl sm:text-5xl font-bold text-white leading-tight mb-6">
                    Affordable eSIMs <span class="text-black block">Data Plans for Every Destination</span>
                </h1>
                <p class="text-lg text-black mb-8 animate-fade-in-delay">
                    Instant digital eSIMs for 190+ destinations. Travel with confidence knowing you're always connected.
                </p>                                
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl mb-20">
                    <form action="{{ route('search') }}" method="GET" class="flex">
                        <div class="flex-1 bg-white border border-white/10 rounded-xl shadow-lg">
                            <input id="autoComplete" type="text" name="query" placeholder="Where are you traveling to?" class="w-full focus:outline-none px-4 py-3 text-black placeholder-gray-400 focus:outline-none rounded-xl" required>
                        </div>
                        <button type="submit" class="ml-2 bg-black text-white px-6 py-3 rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium shadow-lg">
                            <i class="fas fa-search mr-2"></i> Search
                        </button>
                    </form>
                </div>                                
                <div class="bg-yellow-500/10 backdrop-blur-sm border border-black text-black p-4 rounded-2xl inline-flex items-center mb-10 md:mb-24 hidden">
                    <i class="fas fa-tag text-black text-2xl mr-3"></i>
                    <div>
                        <p class="font-bold">Special Offer</p>
                        <p>Use code <span class="font-mono font-bold text-black">TRAVEL25</span> for 25% off your first eSIM!</p>
                    </div>
                </div>
            </div>
            <div class="opacity-2 mt-10 relative w-full animate-fade-in"><img src="{{ url('') }}/GIRAFFE-e1722404145186.avif" class="mr-auto ml-auto md:absolute bottom-0 right-0 w-full md:w-3/4"></div>
        </div>
    </section>        
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Popular Destinations</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Explore our most popular destinations and find the perfect eSIM for your next adventure.
                </p>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($popularCountries as $country)
                    <a href="{{ route('countries.show', $country->slug) }}" 
                       class="group bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-yellow-500/20">
                        <div class="flex items-center gap-4">
                            <div class="relative">
                                <img src="{{ $country->flag_url }}" 
                                     alt="{{ $country->name }}" 
                                     class="w-12 h-12 rounded-xl object-cover border border-gray-100 group-hover:border-yellow-500/50 transition-all duration-300 rounded-full">
                                <div class="absolute inset-0 bg-yellow-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-black transition-colors duration-300">
                                    {{ $country->name }}
                                </h3>
                                <p class="text-sm text-gray-600 mt-1">
                                    From USD ${{ number_format($country->price_from, 2) }}
                                </p>
                            </div>
                            <div class="text-gray-400 group-hover:text-yellow-500 transition-colors duration-300">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-500">No popular destinations available yet. Check back soon!</p>
                    </div>
                @endforelse
            </div>
            
            <div class="text-center mt-12">
                <a href="{{ route('countries.index') }}" 
                   class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300">
                    View All Destinations
                </a>
            </div>
        </div>
    </section>        
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose DataLendR</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Our eSIM technology offers more than just connectivity. Discover the benefits that make us the preferred choice for travelers worldwide.
                </p>
            </div>            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">                
                <div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-all duration-300 border border-gray-100 text-center">
                    <div class="inline-flex items-center justify-center h-50 rounded-2xl bg-yellow-500/10 text-black mb-6">
                        <i class="fas fa-envelope text-5xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Activation code sent directly to your email</h3>
                    <p class="text-gray-600">
                        Get your code instantly by email, no delays, no complex step.
                    </p>
                </div>                                
                <div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-all duration-300 border border-gray-100 text-center">
                    <div class="inline-flex items-center justify-center h-50 rounded-2xl bg-yellow-500/10 text-black mb-6">
                        <i class="fas fa-list-ol text-5xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick and effortless setup</h3>
                    <p class="text-gray-600">
                        Install and connect in just a few taps with our smart design.
                    </p>
                </div>                                
                <div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-all duration-300 border border-gray-100 text-center">
                    <div class="inline-flex items-center justify-center h-50 rounded-2xl bg-yellow-500/10 text-black mb-6">
                        <i class="fas fa-cloud text-5xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Seamless mobile internet connection</h3>
                    <p class="text-gray-600">
                        Stay connected anywhere with fast and reliable mobile data.
                    </p>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <a href="{{ route('features') }}" 
                   class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300">
                    Learn More
                </a>
            </div>
        </div>
    </section>        
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">How It Works</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Getting connected with our eSIM is quick and easy. Just follow these simple steps.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center h-16 w-16 rounded-2xl text-black text-2xl font-bold mb-6"><img src="{{ url('') }}/iphone.png" class="w-full h-full object-cover"></div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Choose a Plan</h3>
                    <p class="text-gray-600">
                        Select Unlimited or Prepaid eSIM plans for your destination country.
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="inline-flex items-center justify-center h-16 w-16 rounded-2xl text-black text-2xl font-bold mb-6"><img src="{{ url('') }}/qr-code-scan.png" class="w-full h-full object-cover"></div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Enter the Activation Code</h3>
                    <p class="text-gray-600">
                        Enter the activation code with your eSIM-compatible device.
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="inline-flex items-center justify-center h-16 w-16 rounded-2xl text-black text-2xl font-bold mb-6"><img src="{{ url('') }}/world-grid.png" class="w-full h-full object-cover"></div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Connect to 4G or 5G</h3>
                    <p class="text-gray-600">
                        Connect to the fastest network and avoid roaming fees.
                    </p>
                </div>
            </div>
        </div>
    </section>        
    <section class="py-20 bg-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div class="mb-14">
                    <h2 class="text-4xl font-bold text-white mb-4">
                        <span class="text-yellow-500">Trusted by</span><br>Travelers Worldwide,<br>Every Day
                    </h2>
                    <p class="text-lg text-gray-300">Don't just take our word for it. Here's what travelers around the world have to say about our eSIM service.</p>
                </div>
                <div class="w-full">
                    @php
                        $testimonials = [
                            [
                                'quote' => "Grazie a DataLendR ho potuto partecipare a una conferenza a Berlino senza preoccuparmi della connessione. Servizio impeccabile!",
                                'name' => 'Luca Bianchi',
                                'role' => 'IT Consultant',
                                'image' => 'https://flagcdn.com/it.svg'
                            ],
                            [
                                'quote' => "J'ai pu partager mes photos en temps réel lors de mon voyage au Vietnam. DataLendR m'a vraiment facilité la vie!",
                                'name' => 'Claire Dubois',
                                'role' => 'Photographer',
                                'image' => 'https://flagcdn.com/fr.svg'
                            ],
                            [
                                'quote' => "Trabajando desde la Patagonia, nunca perdí conexión. DataLendR es esencial para los nómadas digitales.",
                                'name' => 'Juan Pérez',
                                'role' => 'Web Developer',
                                'image' => 'https://flagcdn.com/ar.svg'
                            ],
                            [
                                'quote' => "Während meines Roadtrips durch Skandinavien war ich immer online. Perfekt für Blogger wie mich!",
                                'name' => 'Anna Müller',
                                'role' => 'Travel Blogger',
                                'image' => 'https://flagcdn.com/de.svg'
                            ],
                            [
                                'quote' => "出張中も家族とビデオ通話ができて、本当に助かりました。データレンドR最高です！",
                                'name' => 'Yuki Tanaka',
                                'role' => 'Entrepreneur',
                                'image' => 'https://flagcdn.com/jp.svg'
                            ],
                            [
                                'quote' => "无论是在巴黎还是悉尼，DataLendR都让我轻松上网，出差再也不用担心网络了。",
                                'name' => 'Li Wei',
                                'role' => 'Marketing Manager',
                                'image' => 'https://flagcdn.com/cn.svg'
                            ],
                            [
                                'quote' => "Amerika'da eğitim alırken ailemle iletişimim hiç kesilmedi. DataLendR harika bir çözüm!",
                                'name' => 'Elif Yılmaz',
                                'role' => 'Student',
                                'image' => 'https://flagcdn.com/tr.svg'
                            ],
                            [
                                'quote' => "I streamed live from the Serengeti for my followers thanks to DataLendR. It works even in the wild!",
                                'name' => 'James Smith',
                                'role' => 'Wildlife Photographer',
                                'image' => 'https://flagcdn.com/gb.svg'
                            ],
                            [
                                'quote' => "Viajei pelo Brasil inteiro e nunca fiquei sem internet. DataLendR é indispensável para engenheiros em campo!",
                                'name' => 'Maria Oliveira',
                                'role' => 'Engineer',
                                'image' => 'https://flagcdn.com/br.svg'
                            ]
                        ];
                    @endphp

                    <div class="owl-carousel owl-theme bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-all duration-300 border border-gray-100" id="testimonial-carousel">
                        @foreach($testimonials as $testimonial)
                            <div class="item">
                                <div class="flex items-center mb-6">
                                    <div class="text-yellow-500 text-lg">
                                        <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <p class="text-gray-700 mb-6 text-lg italic">
                                    "{{ $testimonial['quote'] }}"
                                </p>
                                <div class="flex items-center gap-3">
                                    <img src="{{ $testimonial['image'] }}" 
                                        alt="{{ $testimonial['name'] }}" 
                                        class="h-12 w-12 rounded-xl object-cover border-2 border-yellow-500/20">
                                    <div class="text-left">
                                        <h4 class="text-base font-bold text-gray-900">{{ $testimonial['name'] }}</h4>
                                        <p class="text-sm text-gray-500">{{ $testimonial['role'] }}</p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>                
            </div>
        </div>
    </section>        
    <section class="py-20 bg-gray-50 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
            <h2 class="text-3xl font-bold text-black mb-6">Ready to Stay Connected on Your Next Adventure?</h2>
            <p class="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
                Join thousands of satisfied travelers who never worry about staying connected abroad.
            </p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="{{ route('countries.index') }}" 
                   class="inline-flex items-center justify-center px-6 py-3 bg-yellow-500 text-black rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium">
                    Explore Destinations
                </a>
                <a href="{{ route('features') }}" 
                   class="inline-flex items-center justify-center px-6 py-3 border border-black text-black rounded-xl hover:bg-black/10 transition-all duration-300 font-medium">
                    Learn More
                </a>
            </div>
        </div>
    </section>

    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .animate-fade-in-delay {
            animation: fadeIn 0.6s ease-out 0.2s forwards;
            opacity: 0;
        }
    </style>
@endsection
