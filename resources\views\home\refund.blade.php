@extends('layouts.app')
@section('title', 'DataLendR Refund Policy')
@section('content')
    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-3xl font-bold text-white mb-4">Refund Policy</h1>
        </div>
    </section>
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-3xl mx-auto">
                <p class="mb-5 font-bold text-xl">REFUND / CANCELLATION / MODIFICATION POLICY</p>
                <p class="mb-5">The Customer has the right to request a refund or eSIM replacement if the eSIM cannot be installed and used due to a technical problem from DataLendR. Once a QR code is activated, a refund cannot be issued.</p>                
                <p class="mb-5 font-bold text-xl">REFUNDS AND CANCELLATION</p>
                <p class="mb-5">Refund requests can be made within thirty (30) days of purchase under specific conditions. Cooperation with Customer support is required for resolution, and refunds are subject to certain policies and guidelines detailed in the terms and conditions section.</p>                
                <p class="mb-5 font-bold text-xl">MODIFICATION</p>
                <p class="mb-5">No modifications or customizations can be made to purchased eSIM data packages.</p>                
                <p class="mb-5 font-bold text-xl">LIABILITY AND WARRANTY</p>
                <p class="mb-5">DataLendR is not liable for service unavailability and provides no guarantee of network service constant availability.</p>                
                <p class="mb-5">DataLendR is not responsible for any roaming fees charged by your local distributor.</p>                
                <p class="mb-5">For inquiries, contact&nbsp;<EMAIL>.</p>
                <p class="mb-5"></p>
            </div>
        </div>
    </section>
@endsection