@extends('layouts.app')

@section('title', 'Search Countries - DataLendR')

@section('content')
<div class="bg-gray-100 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 class="text-3xl font-bold text-black mb-10">Search Countries</h1>
        <form action="{{ route('search') }}" method="GET" class="mb-8">
            <div class="flex rounded-xl shadow-sm">
                <input type="text" name="query" id="query" class="flex-1 min-w-0 block w-full px-4 py-4 rounded-l-xl border-gray-300 focus:ring-yellow-500 focus:border-yellow-500 text-lg" placeholder="Search for countries...">
                <button type="submit" class="inline-flex items-center px-6 py-4 border border-transparent text-base font-medium rounded-r-xl text-black bg-yellow-500 hover:bg-yellow-400 transition-all duration-300">
                    <i class="fas fa-search mr-2"></i> Search
                </button>
            </div>
        </form>

        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="p-6 bg-white border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Search Results</h2>
            </div>
            <div class="p-6">
                @if(isset($countries) && count($countries) > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($countries as $country)
                        <a href="{{ route('countries.show', $country->slug) }}" 
                        class="group bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-yellow-500/20">
                            <div class="flex items-center gap-4">
                                <div class="relative">
                                    <img src="{{ $country->flag_url }}" 
                                        alt="{{ $country->name }}" 
                                        class="w-12 h-12 rounded-xl object-cover border border-gray-100 group-hover:border-yellow-500/50 transition-all duration-300">
                                    <div class="absolute inset-0 bg-yellow-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-black transition-colors duration-300">
                                        {{ $country->name }}
                                    </h3>
                                    <p class="text-sm text-gray-600 mt-1">
                                        @if($country->price_from)
                                            From USD {{ number_format($country->price_from, 2) }}
                                        @else
                                            View plans
                                        @endif
                                    </p>
                                </div>
                                <div class="text-gray-400 group-hover:text-yellow-500 transition-colors duration-300">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                        </a>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <h3 class="text-xl font-medium text-gray-900 mb-2">No Results Found</h3>
                        <p class="text-gray-500 mb-6">Try searching for a different country.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
