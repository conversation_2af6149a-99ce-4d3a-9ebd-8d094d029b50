@extends('layouts.app')
@section('title', 'DataLendR Terms and Conditions')
@section('content')
    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-3xl font-bold text-white mb-4">Terms and Conditions</h1>
        </div>
    </section>
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-3xl mx-auto">
                <p class="mb-5"><strong>GENERAL TERMS AND CONDITIONS</strong></p>                
                <ul>
                <li class="mb-5"><strong>1. VALIDITY OF GENERAL TERMS AND CONDITIONS</strong></li>
                </ul>
                <p class="mb-5">These Terms and Conditions govern all services provided by DataLendR in connection with prepaid eSIM reselling. They are accessible on the website <a href="https://www.datalendr.com/">https://www.datalendr.com</a>. Any variations to these Terms and Conditions require explicit written agreement.</p>
                <p class="mb-5"> </p>
                <ul>
                <li class="mb-5"><strong>2. DESCRIPTION OF SERVICES</strong></li>
                </ul>
                <p class="mb-5"><strong>2.1. eSIM RESELLING</strong></p>
                <p class="mb-5">DataLendR offers prepaid eSIMs for sale. Customers can register and purchase SIM cards on the <a href="https://www.datalendr.com/">https://www.datalendr.com</a> website. Payments are processed using supported methods such as Credit/Debit Card, Google Pay and Apple Pay.</p>                
                <p class="mb-5"><strong>2.2. REGISTRATION FOR USING DATALENDR SERVICES</strong></p>
                <p class="mb-5">Customers must accept the General Terms and Conditions to use DataLendR services. Registration requires providing necessary information such as First Name, Last Name, Address (billing address), and Email address either directly or through an intermediary service provider.</p>
                <p class="mb-5"> </p>
                <p class="mb-5"><strong>2.3. DATALENDR ENGAGEMENTS</strong></p>
                <p class="mb-5">While DataLendR endeavors to provide quality service, it does not guarantee uninterrupted, timely, secure, or fault-free service.</p>
                <p class="mb-5"> </p>
                <p class="mb-5"><strong>2.4. CUSTOMER ENGAGEMENTS</strong></p>
                <p class="mb-5">Customers must not engage in abusive, illegal, or fraudulent actions that impair or damage the Network. DataLendR reserves the right to suspend services if the Customer breaches their obligations.</p>
                <p class="mb-5"> </p>
                <p class="mb-5"><strong>2.5. DEVICE COMPATIBILITY</strong></p>
                <p class="mb-5">Customers are responsible for ensuring their device is eSIM compatible and network-unlocked. Device compatibility depends on various factors, including carrier and country of origin. Customers must verify compatibility before purchase.</p>
                <p class="mb-5"> </p>
                <ul>
                <li class="mb-5"><strong>3. START, DURATION, AND TERMINATION OF THE CONTRACT</strong></li>
                </ul>
                <p class="mb-5">The service contract between DataLendR and the Customer begins upon completing the order at <a href="https://www.datalendr.com/">https://www.datalendr.com.</a> Activation of the eSIM and acknowledgment of the Activation Policy is the Customer’s responsibility. The contract terminates if the Customer lacks an active data package or deletes the eSIM from the target device.</p>
                <p class="mb-5"> </p>
                <ul>
                <li class="mb-5"><strong>4. CHARGES AND PAYMENT</strong></li>
                </ul>
                <p class="mb-5"><strong>4.1. PAYMENT CONDITIONS</strong></p>
                <p class="mb-5">Supported payment methods include Credit/Debit Card, Google Pay, Apple Pay, and Alipay. All transactions are in US Dollars ($).</p>
                <p class="mb-5"> </p>
                <p class="mb-5"><strong>4.2. CHARGES FOR USE</strong></p>
                <p class="mb-5">Charges are inclusive of VAT unless stated otherwise. The Customer cannot offset claims against DataLendR except in certain circumstances outlined in our Terms and Conditions.</p>
                <p class="mb-5"> </p>
                <ul>
                <li class="mb-5"><strong>5. DELIVERY</strong></li>
                </ul>
                <p class="mb-5">Upon purchase, the Customer will receive an email containing the QR code of the eSIM. Installation instructions are accessible through the step-by-step page in the FAQ section. It is the Customer’s responsibility to correctly install the eSIM.</p>
                <p class="mb-5"> </p>
                <ul>
                <li class="mb-5"><strong>6. REFUND / CANCELLATION / MODIFICATION POLICY</strong></li>
                </ul>
                <p class="mb-5"><strong>6.1. REFUNDS AND CANCELLATION</strong></p>
                <p class="mb-5">Refund requests can be made within thirty (30) days of purchase under specific conditions. Cooperation with Customer support is required for resolution, and refunds are subject to certain policies and guidelines detailed in this section.</p>
                <p class="mb-5"> </p>
                <p class="mb-5"><strong>6.2. MODIFICATION</strong></p>
                <p class="mb-5">No modifications or customizations can be made to purchased eSIM data packages.</p>
                <p class="mb-5"> </p>
                <ul>
                <li class="mb-5"><strong>7. LIABILITY AND WARRANTY</strong></li>
                </ul>
                <p class="mb-5">DataLendR is not liable for service unavailability and provides no guarantee of network service constant availability.</p>
                <p class="mb-5">DataLendR is not responsible for any roaming fees charged by your local distributor.</p>
                <p class="mb-5"> </p>
                <p class="mb-5">For inquiries, contact <strong><EMAIL></strong>.</p>
            </div>
        </div>
    </section>
@endsection