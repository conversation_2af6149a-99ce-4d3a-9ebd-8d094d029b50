<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
	<meta property="og:locale" content="en_US" />
	<meta property="og:type" content="website" />
	<meta property="og:title" content="DataLendR - Get Data, Simply, Anywhere." />
	<meta property="og:description" content="We simplify global connectivity with affordable eSIM solutions for travelers. Stay connected anywhere, anytime—without roaming fees." />
	<meta property="og:url" content="https://datalendr.com/" />
	<meta property="og:site_name" content="DataLendR" />
	<meta property="article:publisher" content="https://www.facebook.com/profile.php?id=61570153319248" />
	<meta property="article:modified_time" content="2025-03-25T02:17:15+00:00" />
	<meta property="og:image" content="{{ url('DataLendR-Main-Logo.jpg') }}" />
	<meta property="og:image:width" content="2400" />
	<meta property="og:image:height" content="1800" />
	<meta property="og:image:type" content="image/jpeg" />
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content="DataLendR - Get Data, Simply, Anywhere." />
	<meta name="twitter:image" content="{{ url('DataLendR-Main-Logo.jpg') }}" />
	<meta name="twitter:site" content="@datalendr" />
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="DataLendR" />
    <link rel="manifest" href="/site.webmanifest" />
    
	<script type="application/ld+json" class="yoast-schema-graph">{"@context":"https://schema.org","@graph":[{"@type":"WebPage","@id":"https://datalendr.com/","url":"https://datalendr.com/","name":"DataLendR - Get Data, Simply, Anywhere.","isPartOf":{"@id":"https://datalendr.com/#website"},"about":{"@id":"https://datalendr.com/#organization"},"primaryImageOfPage":{"@id":"https://datalendr.com/#primaryimage"},"image":{"@id":"https://datalendr.com/#primaryimage"},"thumbnailUrl":"{{ url('DataLendR-Main-Logo.jpg') }}","datePublished":"2019-06-18T10:21:17+00:00","dateModified":"2025-03-25T02:17:15+00:00","description":"We simplify global connectivity with affordable eSIM solutions for travelers. Stay connected anywhere, anytime—without roaming fees.","breadcrumb":{"@id":"https://datalendr.com/#breadcrumb"},"inLanguage":"en-US","potentialAction":[{"@type":"ReadAction","target":["https://datalendr.com/"]}]},{"@type":"ImageObject","inLanguage":"en-US","@id":"https://datalendr.com/#primaryimage","url":"{{ url('DataLendR-Main-Logo.jpg') }}","contentUrl":"{{ url('DataLendR-Main-Logo.jpg') }}","width":2400,"height":1800},{"@type":"BreadcrumbList","@id":"https://datalendr.com/#breadcrumb","itemListElement":[{"@type":"ListItem","position":1,"name":"Home"}]},{"@type":"WebSite","@id":"https://datalendr.com/#website","url":"https://datalendr.com/","name":"DataLendR","description":"Get Data, Simply, Anywhere.","publisher":{"@id":"https://datalendr.com/#organization"},"potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://datalendr.com/?s={search_term_string}"},"query-input":{"@type":"PropertyValueSpecification","valueRequired":true,"valueName":"search_term_string"}}],"inLanguage":"en-US"},{"@type":"Organization","@id":"https://datalendr.com/#organization","name":"DataLendR","url":"https://datalendr.com/","logo":{"@type":"ImageObject","inLanguage":"en-US","@id":"https://datalendr.com/#/schema/logo/image/","url":"{{ url('DataLendR-Main-Logo.jpg') }}","contentUrl":"{{ url('DataLendR-Main-Logo.jpg') }}","width":2400,"height":1800,"caption":"DataLendR"},"image":{"@id":"https://datalendr.com/#/schema/logo/image/"},"sameAs":["https://www.facebook.com/profile.php?id=61570153319248","https://x.com/datalendr","https://instagram.com/datalendr","https://www.tiktok.com/@datalendr"]}]}</script>
	<!-- / Yoast SEO plugin. -->
    <title>@yield('title', config('app.name', 'DataLendR - Get Data, Simply, Anywhere.')) - Get Data, Simply, Anywhere.</title>
    <meta property="description" content="We simplify global connectivity with affordable eSIM solutions for travelers. Stay connected anywhere, anytime—without roaming fees." />
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ asset('css/payment.css') }}">
    <link rel="stylesheet" href="{{ asset('css/stripe-elements.css') }}">
    
    <style>
        :root {
            --color-primary: #fef952;
            --color-secondary: #3b82f6;
            --color-dark: #1f2937;
            --color-light: #f9fafb;
            --color-yellow: #fef952;
        }
        .bg-yellow-500 {
            background: var(--color-primary);
        }
        .hover\:bg-yellow-500:hover{
            background: var(--color-primary);
        }
        .text-yellow-500,.hover\:text-yellow-500:hover{
            color: var(--color-primary);
        }
        .bg-primary { background-color: var(--color-primary); }
        .bg-secondary { background-color: var(--color-secondary); }
        .bg-dark { background-color: var(--color-dark); }
        
        .text-primary { color: var(--color-primary); }
        .text-secondary { color: var(--color-secondary); }
        
        .btn-primary {
            display: inline-block;
            background-color: var(--color-primary);
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: background-color 0.3s;
        }
        
        .btn-primary:hover {
            background-color: #e69009;
        }
        
        .btn-secondary {
            display: inline-block;
            background-color: var(--color-secondary);
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: background-color 0.3s;
        }
        
        .btn-secondary:hover {
            background-color: #2563eb;
        }
        
        .btn-white {
            display: inline-block;
            background-color: white;
            color: var(--color-dark);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: background-color 0.3s;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }
        
        .btn-white:hover {
            background-color: #f9fafb;
        }
    </style>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-N5GNB744');</script>
    <!-- End Google Tag Manager -->
     <script id="Cookiebot" src="https://consent.cookiebot.com/uc.js" data-cbid="3872975c-084e-4708-ab21-d46614ccd929" data-blockingmode="auto" type="text/javascript"></script>
    @yield('head')
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50 text-gray-800">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N5GNB744"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->    
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-black shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-20">
                    <div class="flex">
                        <!-- Logo -->
                        <div class="flex-shrink-0 flex items-center">
                            <a href="{{ route('home') }}" class="text-3xl font-bold text-white">
                                <img src="{{ url('DataLendR-Main-Logo-4.png') }}" width="150">
                            </a>
                        </div>                        
                        
                        <div id="main-menu" class="fixed top-0 left-0 h-full w-60 md:w-full bg-black z-50 transform -translate-x-full transition-transform duration-300 px-6 pt-24 pb-8 sm:static sm:translate-x-0 sm:flex sm:items-center sm:space-x-8 sm:ml-10 sm:bg-transparent sm:p-0 hidden items-center">

                            <a href="{{ route('home') }}" 
                               class="text-base font-medium text-gray-300 hover:text-yellow-500 transition-colors duration-300 block md:inline py-3 md:py-0 md:hidden">
                                Home
                            </a>

                            <a href="{{ route('countries.index') }}" 
                               class="text-base font-medium text-gray-300 hover:text-yellow-500 transition-colors duration-300 block md:inline py-3 md:py-0">
                                Destinations
                            </a>
                            <a href="{{ route('features') }}" 
                               class="text-base font-medium text-gray-300 hover:text-yellow-500 transition-colors duration-300 block md:inline py-3 md:py-0">
                                Features
                            </a>
                            <a href="{{ route('about') }}" 
                               class="text-base font-medium text-gray-300 hover:text-yellow-500 transition-colors duration-300 block md:inline py-3 md:py-0">
                                About Us
                            </a>
                            <a href="{{ route('blog') }}" 
                               class="text-base font-medium text-gray-300 hover:text-yellow-500 transition-colors duration-300 block md:inline py-3 md:py-0">
                                Blog
                            </a>
                            <a href="{{ route('help') }}" 
                               class="text-base font-medium text-gray-300 hover:text-yellow-500 transition-colors duration-300 block md:inline py-3 md:py-0">
                                Help Center
                            </a>
                        </div>
                        <div id="main-menu-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden sm:hidden"></div>
                    </div>
                    
                    <div class="flex items-center space-x-6">
                        @if(session()->has('cart'))
                        <a href="{{ route('checkout') }}" 
                           class="relative p-2 text-gray-300 hover:text-yellow-500 transition-colors duration-300">
                            <i class="fas fa-shopping-cart text-xl"></i>
                            @if(session()->has('cart') && count(session('cart')) > 0)
                                <span class="absolute -top-1 -right-1 inline-flex items-center justify-center w-5 h-5 text-xs font-bold leading-none text-black transform translate-x-1/2 -translate-y-1/2 bg-yellow-500 rounded-full">
                                    {{ count(session('cart')) }}
                                </span>
                            @endif
                        </a>
                        @endif
                        
                        <!-- Authentication Links -->
                        <div class="flex items-center space-x-4">
                            @guest
                                <a href="{{ route('login') }}" 
                                   class="inline-flex items-center px-6 py-3 bg-yellow-500 text-black rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium">
                                    Login
                                </a>
                            @else
                                <div class="relative" x-data="{ open: false }">
                                    <button @click="open = !open" 
                                            class="flex items-center text-gray-300 hover:text-yellow-500 transition-colors duration-300">
                                        <span class="mr-2">{{ Auth::user()->name }}</span>
                                        <i class="fas fa-chevron-down text-sm"></i>
                                    </button>
                                    
                                    <div x-show="open" 
                                         @click.away="open = false" 
                                         class="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg py-2 z-50">
                                        <a href="{{ route('dashboard.index') }}" 
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-black transition-colors duration-300">
                                            Dashboard
                                        </a>
                                        <a href="{{ route('orders.index') }}" 
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-black transition-colors duration-300">
                                            My Orders
                                        </a>
                                        <a href="{{ route('dashboard.esims') }}" 
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-black transition-colors duration-300">
                                            My eSIMs
                                        </a>
                                        <a href="{{ route('dashboard.settings') }}" 
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-black transition-colors duration-300">
                                            Account Settings
                                        </a>
                                        <div class="border-t border-gray-100 my-1"></div>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" 
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-black transition-colors duration-300">
                                                Logout
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            @endguest
                        </div>
                        <button type="button" class="sm:hidden flex items-center px-2 py-2 rounded-md text-gray-300 hover:text-yellow-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-yellow-500" id="menu-toggle">
                            <span class="sr-only">Open main menu</span>
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>    

        <main class="flex-1">
            @if(session('success'))
                <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded" role="alert">
                    <p>{{ session('success') }}</p>
                </div>
            @endif
            
            @if(session('error'))
                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded" role="alert">
                    <p>{{ session('error') }}</p>
                </div>
            @endif            
            @yield('content')
        </main>
        
        <footer class="bg-black text-white py-16 relative overflow-hidden">
            <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
                    <div>
                        <h3 class="text-xl font-bold mb-6">DataLendR</h3>
                        <p class="text-gray-400 leading-relaxed">
                            Stay connected anywhere in the world with our affordable eSIM plans. Experience seamless connectivity with our digital SIM technology.
                        </p>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold mb-6 text-yellow-500">Quick Links</h3>
                        <ul class="space-y-4">
                            <li>
                                <a href="{{ route('home') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Home
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('countries.index') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Destinations
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('features') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Features
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('blog') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Blog
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('about') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> About Us
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold mb-6 text-yellow-500">Support</h3>
                        <ul class="space-y-4">
                            <li>
                                <a href="{{ route('help') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Help Center
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('contact') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Contact Us
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('privacy') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Privacy Policy
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('refund') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Refund Policy
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('terms') }}" class="text-gray-400 hover:text-yellow-500 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-chevron-right text-xs mr-2"></i> Terms and Conditions
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold mb-6 text-yellow-500">Connect With Us</h3>
                        <div class="flex space-x-4">
                            <a href="https://www.facebook.com/profile.php?id=61570153319248" class="w-10 h-10 rounded-xl bg-white/10 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-white/20 transition-all duration-300">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://x.com/datalendr" class="w-10 h-10 rounded-xl bg-white/10 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-white/20 transition-all duration-300">
                                <i class="fab fa-x-twitter"></i>
                            </a>
                            <a href="https://www.instagram.com/datalendr" class="w-10 h-10 rounded-xl bg-white/10 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-white/20 transition-all duration-300">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="https://www.tiktok.com/@datalendr" class="w-10 h-10 rounded-xl bg-white/10 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-white/20 transition-all duration-300">
                                <i class="fab fa-tiktok"></i>
                            </a>
                        </div>                        
                    </div>
                </div>
                
                <div class="mt-12 pt-8 text-left">
                    <p class="text-gray-400">&copy; {{ date('Y') }} DataLendR. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>
    
    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Payment Helpers -->
    <script src="{{ asset('js/payment-helpers.js') }}"></script>
    
    @yield('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        var menuToggle = document.getElementById('menu-toggle');
        var mainMenu = document.getElementById('main-menu');
        var backdrop = document.getElementById('main-menu-backdrop');

        function openMenu() {
            mainMenu.style.display = 'block';
            setTimeout(function() {
                mainMenu.classList.remove('-translate-x-full');
            }, 10);
            if(backdrop) backdrop.classList.remove('hidden');
        }
        function closeMenu() {
            mainMenu.classList.add('-translate-x-full');
            if(backdrop) backdrop.classList.add('hidden');
            setTimeout(function() {
                mainMenu.style.display = '';
            }, 300);
        }
        if(menuToggle && mainMenu) {
            menuToggle.addEventListener('click', function() {
                if(mainMenu.classList.contains('-translate-x-full')) {
                    openMenu();
                } else {
                    closeMenu();
                }
            });
        }
        if(backdrop) {
            backdrop.addEventListener('click', closeMenu);
        }
        // Hide menu on resize to desktop
        window.addEventListener('resize', function() {
            if(window.innerWidth >= 640) { // sm breakpoint
                mainMenu.style.display = '';
                mainMenu.classList.remove('-translate-x-full');
                if(backdrop) backdrop.classList.add('hidden');
            } else {
                mainMenu.classList.add('-translate-x-full');
                mainMenu.style.display = '';
                if(backdrop) backdrop.classList.add('hidden');
            }
        });
    });
    </script>
    <script src="//code.tidio.co/ialdfpwmvo8oh0dtks5zlva9s5tzjoal.js" async></script>
    <script type="text/javascript" src="//script.crazyegg.com/pages/scripts/0129/9130.js" async="async"></script>
</body>
</html>
