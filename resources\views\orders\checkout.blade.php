@extends('layouts.app')

@section('content')
    <!-- Checkout Header -->
    <section class="bg-black py-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
            <h1 class="text-3xl font-bold text-white mb-4 animate-fade-in">Checkout</h1>
            <p class="text-xl text-gray-300 animate-fade-in-delay">
                Complete your purchase to get connected
            </p>
        </div>
    </section>
    
    <!-- Checkout Content -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Checkout Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-100">
                        <div class="p-6 bg-gray-50 border-b">
                            <h2 class="text-lg font-semibold text-gray-900">Payment Information</h2>
                        </div>
                        
                        <div class="p-6">
                            <form action="{{ route('checkout.process') }}" method="POST" id="payment-form">
                                @csrf
                                
                                <!-- Payment Method -->
                                <div class="mb-8">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
                                    
                                    <div class="space-y-4">
                                        <div class="flex items-center">
                                            <input id="card" name="payment_method" type="radio" checked 
                                                   class="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300">
                                            <label for="card" class="ml-3 block text-sm font-medium text-gray-700">
                                                Credit or Debit Card
                                            </label>
                                        </div>
                                        
                                        <div class="ml-7 mt-2">
                                            <div class="flex space-x-2 mb-6">
                                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/visa.svg" alt="Visa" class="h-8">
                                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/mastercard.svg" alt="Mastercard" class="h-8">
                                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/amex.svg" alt="American Express" class="h-8">
                                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/discover.svg" alt="Discover" class="h-8">
                                            </div>
                                            
                                            <div id="payment-element" data-stripeKey="{{ config('services.stripe.key') }}">
                                                <div class="space-y-4">
                                                    <div>
                                                        <label for="card_number" class="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                                                        <input type="text" id="card_number" name="card_number" 
                                                               placeholder="1234 5678 9012 3456" 
                                                               class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                               required>
                                                    </div>
                                                    
                                                    <div class="grid grid-cols-2 gap-4">
                                                        <div>
                                                            <label for="expiry" class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                                                            <input type="text" id="expiry" name="expiry" 
                                                                   placeholder="MM/YY" 
                                                                   class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                                   required>
                                                        </div>
                                                        <div>
                                                            <label for="cvc" class="block text-sm font-medium text-gray-700 mb-1">CVC</label>
                                                            <input type="text" id="cvc" name="cvc" 
                                                                   placeholder="123" 
                                                                   class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                                   required>
                                                        </div>
                                                    </div>
                                                    
                                                    <div>
                                                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name on Card</label>
                                                        <input type="text" id="name" name="name" 
                                                               placeholder="John Doe" 
                                                               class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                               required>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="flex items-center mt-4">
                                                <input id="paypal" name="payment_method" type="radio" 
                                                       class="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300">
                                                <label for="paypal" class="ml-3 block text-sm font-medium text-gray-700">
                                                    PayPal
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Billing Address -->
                                <div class="mb-8 pt-8 border-t">
                                    <h3 class="text-lg font-medium text-gray-900 mb-6">Billing Address</h3>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                            <input type="text" id="first_name" name="first_name" 
                                                   class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                   required>
                                        </div>
                                        <div>
                                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                            <input type="text" id="last_name" name="last_name" 
                                                   class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                   required>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                                        <input type="text" id="address" name="address" 
                                               class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                               required>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                        <div>
                                            <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                            <input type="text" id="city" name="city" 
                                                   class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                   required>
                                        </div>
                                        <div>
                                            <label for="state" class="block text-sm font-medium text-gray-700 mb-1">State/Province</label>
                                            <input type="text" id="state" name="state" 
                                                   class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                   required>
                                        </div>
                                        <div>
                                            <label for="zip" class="block text-sm font-medium text-gray-700 mb-1">ZIP/Postal Code</label>
                                            <input type="text" id="zip" name="zip" 
                                                   class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                   required>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                                        <select id="country" name="country" 
                                                class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                                required>
                                            <option value="">Select a country</option>
                                            <option value="US">United States</option>
                                            <option value="CA">Canada</option>
                                            <option value="GB">United Kingdom</option>
                                            <option value="AU">Australia</option>
                                            <option value="DE">Germany</option>
                                            <option value="FR">France</option>
                                            <option value="JP">Japan</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Contact Information -->
                                <div class="mb-8 pt-8 border-t">
                                    <h3 class="text-lg font-medium text-gray-900 mb-6">Contact Information</h3>
                                    
                                    <div>
                                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                        <input type="email" id="email" name="email" 
                                               class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                               value="{{ Auth::user()->email }}" 
                                               required>
                                        <p class="text-sm text-gray-500 mt-1">
                                            Your eSIM and receipt will be sent to this email address.
                                        </p>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                        <input type="tel" id="phone" name="phone" 
                                               class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all duration-300 outline-none" 
                                               required>
                                    </div>
                                </div>
                                
                                <!-- Terms and Conditions -->
                                <div class="mb-8">
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="terms" name="terms" type="checkbox" 
                                                   class="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300 rounded" 
                                                   required>
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="terms" class="font-medium text-gray-700">
                                                I agree to the <a href="#" class="text-yellow-500 hover:text-yellow-600">Terms of Service</a> and <a href="#" class="text-yellow-500 hover:text-yellow-600">Privacy Policy</a>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-8">
                                    <button type="submit" 
                                            class="w-full bg-black text-white px-6 py-3 rounded-xl hover:bg-gray-800 transition-all duration-300 font-medium">
                                        Complete Purchase
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div>
                    <div class="bg-white rounded-2xl shadow-sm p-6 border border-gray-100 sticky top-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Order Summary</h2>
                        
                        <div class="divide-y">
                            @foreach($cart as $id => $item)
                                <div class="py-4">
                                    <div class="flex justify-between mb-1">
                                        <span class="font-medium">{{ $item['name'] }}</span>
                                        <span>${{ number_format($item['price'], 2) }}</span>
                                    </div>
                                    <p class="text-sm text-gray-600">
                                        {{ $item['country'] }} - {{ $item['data_amount'] }} for {{ $item['validity_days'] }} days
                                    </p>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="space-y-3 mt-6 pt-6 border-t">
                            @php
                                $subtotal = 0;
                                foreach($cart as $item) {
                                    $subtotal += $item['price'];
                                }
                                
                                // For demonstration purposes, let's assume there's no tax
                                $tax = 0;
                                $discount = $subtotal * 0.25; // 25% discount
                                $total = $subtotal - $discount + $tax;
                            @endphp
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal:</span>
                                <span class="font-medium">${{ number_format($subtotal, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Discount (TRAVEL25):</span>
                                <span class="font-medium text-yellow-500">-${{ number_format($discount, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tax:</span>
                                <span class="font-medium">${{ number_format($tax, 2) }}</span>
                            </div>
                        </div>
                        
                        <div class="border-t py-6 mt-6">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-900 font-semibold">Total:</span>
                                <span class="text-2xl font-bold text-black">${{ number_format($total, 2) }}</span>
                            </div>
                        </div>
                        
                        <div class="mt-6 text-center text-sm text-gray-600">
                            <p class="flex items-center justify-center mb-4">
                                <i class="fas fa-lock mr-2 text-yellow-500"></i> Secure checkout
                            </p>
                            <div class="flex justify-center space-x-2">
                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/visa.svg" alt="Visa" class="h-6">
                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/mastercard.svg" alt="Mastercard" class="h-6">
                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/amex.svg" alt="American Express" class="h-6">
                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/discover.svg" alt="Discover" class="h-6">
                                <img src="https://cdn.jsdelivr.net/npm/payment-icons@1.0.0/min/flat/paypal.svg" alt="PayPal" class="h-6">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .animate-fade-in-delay {
            animation: fadeIn 0.6s ease-out 0.2s forwards;
            opacity: 0;
        }
    </style>
@endsection

@section('scripts')
<script src="https://js.stripe.com/v3/"></script>
@endsection
