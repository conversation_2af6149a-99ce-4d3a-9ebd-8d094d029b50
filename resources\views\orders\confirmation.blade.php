@extends('layouts.app')

@section('content')
    <!-- Confirmation Header -->
    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="inline-flex items-center justify-center h-24 w-24 rounded-full bg-white mb-6">
                <i class="fas fa-check text-5xl text-green-600"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Order Confirmed!</h1>
            <p class="text-xl text-white max-w-2xl mx-auto">
                Thank you for your purchase. Your eSIM has been successfully processed and is ready for activation.
            </p>
        </div>
    </section>
    
    <!-- Order Details -->
    <section class="py-12 bg-white">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                <div class="p-6 bg-white border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Order Details</h2>
                </div>
                
                <div class="p-6">
                    <div class="flex flex-col md:flex-row justify-between mb-6 pb-6 border-b">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">Order Number</p>
                            <p class="font-medium">{{ $order->order_number }}</p>
                        </div>
                        <div class="mt-4 md:mt-0">
                            <p class="text-sm text-gray-500 mb-1">Date</p>
                            <p class="font-medium">{{ $order->created_at->format('F j, Y') }}</p>
                        </div>
                        <div class="mt-4 md:mt-0">
                            <p class="text-sm text-gray-500 mb-1">Total</p>
                            <p class="font-medium">${{ number_format($order->amount, 2) }}</p>
                        </div>
                        <div class="mt-4 md:mt-0">
                            <p class="text-sm text-gray-500 mb-1">Status</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ ucfirst($order->payment_status) }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">eSIM Information</h3>
                        
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <div class="flex flex-col md:flex-row">
                                <div class="md:w-1/2 mb-4 md:mb-0">
                                    <p class="text-sm text-gray-500 mb-1">eSIM Plan</p>
                                    <p class="font-medium">{{ $order->esim->name }}</p>
                                    <p class="text-sm text-gray-600 mt-1">{{ $order->esim->country->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $order->esim->data_amount }} for {{ $order->esim->validity_days }} days</p>
                                </div>
                                <div class="md:w-1/2 flex justify-center md:justify-end">
                                    @if($order->qr_code_url)
                                        <div class="text-center">
                                            <img src="{{ $order->qr_code_url }}" alt="eSIM QR Code" class="h-32 w-32 mx-auto">
                                            <p class="text-sm text-gray-500 mt-2">Scan this QR code to install your eSIM</p>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <div class="h-32 w-32 mx-auto bg-gray-200 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-qrcode text-4xl text-gray-400"></i>
                                            </div>
                                            <p class="text-sm text-gray-500 mt-2">QR code will be sent to your email</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        @if($order->activation_code)
                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <p class="text-sm text-gray-500 mb-1">Activation Code</p>
                                <p class="font-mono font-medium text-lg">{{ $order->activation_code }}</p>
                                <p class="text-sm text-gray-500 mt-1">You may need this code during the eSIM installation process.</p>
                            </div>
                        @endif
                        
                        @if($order->instructions)
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm text-gray-500 mb-1">Installation Instructions</p>
                                <div class="prose prose-sm max-w-none">
                                    <p>{!! $order->instructions !!}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Next Steps</h3>
                        
                        <div class="space-y-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-8 w-8 rounded-full bg-yellow-500 text-black">
                                        1
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-medium text-gray-900">Check Your Email</h4>
                                    <p class="text-sm text-gray-600">
                                        We've sent a confirmation email to {{ $user->email }} with your eSIM details and installation instructions.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-8 w-8 rounded-full bg-yellow-500 text-black">
                                        2
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-medium text-gray-900">Install Your eSIM</h4>
                                    <p class="text-sm text-gray-600">
                                        Follow the instructions in your email to install the eSIM on your device. You can also scan the QR code above.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-8 w-8 rounded-full bg-yellow-500 text-black">
                                        3
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-medium text-gray-900">Activate When Ready</h4>
                                    <p class="text-sm text-gray-600">
                                        Your eSIM is ready to use. Activate it when you arrive at your destination or whenever you need it.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex flex-col md:flex-row justify-between space-y-4 md:space-y-0">
                        <a href="{{ route('orders.index') }}" class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300">
                            View All Orders
                        </a>
                        <a href="{{ route('countries.index') }}" class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300">
                            Continue Shopping
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Support Section -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Need Help?</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
                Our support team is available 24/7 to assist you with any questions or issues you may have.
            </p>
            
            <div class="flex flex-col md:flex-row justify-center gap-4">
                <a href="{{ route('help') }}" class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300">
                    <i class="fas fa-question-circle mr-2"></i> Visit Help Center
                </a>
                <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300">
                    <i class="fas fa-envelope mr-2"></i> Email Support
                </a>
            </div>
        </div>
    </section>
@endsection
