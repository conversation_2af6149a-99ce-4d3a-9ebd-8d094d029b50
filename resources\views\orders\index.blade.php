@extends('layouts.app')

@section('content')
    <section class="bg-black py-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2 animate-fade-in">My Orders</h1>
                    <p class="text-xl text-gray-300 animate-fade-in-delay">View and manage your eSIM purchases</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('countries.index') }}" 
                       class="inline-flex items-center px-6 py-3 bg-yellow-500 text-black rounded-xl hover:bg-yellow-400 transition-all duration-300 font-medium">
                        <i class="fas fa-plus mr-2"></i> Buy New eSIM
                    </a>
                </div>
            </div>
        </div>
    </section>    
    <!-- Orders List -->
    <section class="py-16 bg-white min-h-[40vh] bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if(count($orders) > 0)
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div class="p-6 bg-gray-100 border-b">
                        <h2 class="text-2xl font-bold text-gray-900 flex items-center gap-2"><i class="fa-solid fa-clock-rotate-left text-black"></i> Order History</h2>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Order #</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">eSIM Plan</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Amount</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($orders as $order)
                                    <tr class="hover:bg-gray-100 transition">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-base font-semibold text-gray-900">{{ $order->order_number }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $order->created_at->format('M d, Y') }}</div>
                                            <div class="text-xs text-gray-500">{{ $order->created_at->format('h:i A') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-base font-semibold text-gray-900">{{ $order->esim->name }}</div>
                                            <div class="text-xs text-gray-500">{{ $order->esim->country->name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-block text-sm text-block font-bold">${{ number_format($order->amount, 2) }}</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="{{ route('orders.show', $order->order_number) }}" class="inline-flex items-center gap-2 bg-black text-white font-semibold px-5 py-2 rounded-lg shadow transition">
                                                <i class="fa-solid fa-eye"></i> View Details
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @else
                <div class="bg-white rounded-2xl shadow-lg p-12 text-center flex flex-col items-center justify-center">
                    <div class="inline-flex items-center justify-center h-20 w-20 rounded-full bg-gray-100 text-gray-400 mb-6">
                        <i class="fas fa-sim-card text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No Active eSIMs</h3>
                    <p class="text-gray-500 mb-6 max-w-md mx-auto">
                        You don't have any active eSIMs yet. Purchase your first eSIM to get started with global connectivity.
                    </p>
                    <a href="{{ route('countries.index') }}" class="bg-yellow-500 text-black text-sm p-2 rounded-lg hover:bg-yellow-400">
                        Browse eSIMs
                    </a>
                </div>
            @endif
        </div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-2"><i class="fa-solid fa-circle-question text-black"></i> Frequently Asked Questions</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">Find answers to common questions about your orders and eSIMs.</p>
            </div>
            <div class="max-w-3xl mx-auto space-y-8">
                <div class="bg-white rounded-xl shadow p-8 flex gap-4 items-start">
                    <i class="fa-solid fa-qrcode text-2xl text-black mt-1"></i>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">How do I install my eSIM?</h3>
                        <p class="text-gray-600">After purchasing an eSIM, you'll receive a QR code. On your device, go to Settings &gt; Cellular/Mobile Data &gt; Add Cellular/Mobile Plan, then scan the QR code. Follow the on-screen instructions to complete the installation.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow p-8 flex gap-4 items-start">
                    <i class="fa-solid fa-bolt text-2xl text-black mt-1"></i>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">When does my eSIM activate?</h3>
                        <p class="text-gray-600">Your eSIM will activate when you first connect to a network in your destination country. The validity period begins from the moment of activation, not from the purchase date.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow p-8 flex gap-4 items-start">
                    <i class="fa-solid fa-mobile-screen-button text-2xl text-black mt-1"></i>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">Can I use my eSIM on multiple devices?</h3>
                        <p class="text-gray-600">No, each eSIM is linked to a specific device and cannot be transferred. If you need connectivity for multiple devices, you'll need to purchase separate eSIMs for each device.</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow p-8 flex gap-4 items-start">
                    <i class="fa-solid fa-plus text-2xl text-black mt-1"></i>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">What if I need more data?</h3>
                        <p class="text-gray-600">If you need more data, you can purchase an additional eSIM at any time. Your new eSIM will be delivered instantly to your email, and you can install it following the same process.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection