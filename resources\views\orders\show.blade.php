@extends('layouts.app')

@section('content')    
    <section class="bg-black text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">Order Details</h1>
                    <p class="text-xl text-white">
                        Order #{{ $order->order_number }}
                    </p>
                </div>
                <div class="mt-4 md:mt-0">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $order->payment_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ ucfirst($order->payment_status) }}
                    </span>
                </div>
            </div>
        </div>
    </section>
        
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">                
                <div class="lg:col-span-2">
                    <div class="bg-white border rounded-lg shadow-sm overflow-hidden mb-8">
                        <div class="p-4 bg-white border-b">
                            <h2 class="text-lg font-semibold text-gray-900">Order Information</h2>
                        </div>
                        
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-1">Order Number</h3>
                                    <p class="text-base font-medium text-gray-900">{{ $order->order_number }}</p>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-1">Order Date</h3>
                                    <p class="text-base font-medium text-gray-900">{{ $order->created_at->format('F j, Y') }}</p>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-1">Payment Status</h3>
                                    <p class="text-base font-medium text-gray-900">{{ ucfirst($order->payment_status) }}</p>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 mb-1">Payment Method</h3>
                                    <p class="text-base font-medium text-gray-900">Credit Card</p>
                                </div>
                            </div>
                            
                            <div class="border-t pt-6">
                                <h3 class="text-base font-medium text-gray-900 mb-4">eSIM Details</h3>
                                
                                <div class="flex flex-col md:flex-row">
                                    <div class="md:w-1/2 mb-4 md:mb-0">
                                        <p class="text-sm text-gray-500 mb-1">eSIM Plan</p>
                                        <p class="font-medium">{{ $order->esim->name }}</p>
                                        <p class="text-sm text-gray-600 mt-1">{{ $order->esim->country->name }}</p>
                                        <p class="text-sm text-gray-600">{{ $order->esim->data_amount }} for {{ $order->esim->validity_days }} days</p>
                                    </div>
                                    <div class="md:w-1/2 flex justify-center md:justify-end">
                                        @if($order->qr_code_url)
                                            <div class="text-center">
                                                <img src="{{ $order->qr_code_url }}" alt="eSIM QR Code" class="h-32 w-32 mx-auto">
                                                <p class="text-sm text-gray-500 mt-2">Scan this QR code to install your eSIM</p>
                                            </div>
                                        @else
                                            <div class="text-center">
                                                <div class="h-32 w-32 mx-auto bg-gray-200 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-qrcode text-4xl text-gray-400"></i>
                                                </div>
                                                <p class="text-sm text-gray-500 mt-2">QR code will be sent to your email</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                
                                @if($order->activation_code)
                                    <div class="bg-gray-50 rounded-lg p-4 mt-6">
                                        <p class="text-sm text-gray-500 mb-1">Activation Code</p>
                                        <p class="font-mono font-medium text-lg">{{ $order->activation_code }}</p>
                                        <p class="text-sm text-gray-500 mt-1">You may need this code during the eSIM installation process.</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
                        <div class="p-6 bg-white border-b">
                            <h2 class="text-lg font-semibold text-gray-900">Installation Instructions</h2>
                        </div>
                        
                        <div class="p-6">
                            @if($order->instructions)
                                <div class="prose max-w-none">
                                    <p>{!! $order->instructions !!}</p>
                                </div>
                            @else
                                <div class="space-y-6">
                                    <div>
                                        <h3 class="text-base font-medium text-gray-900 mb-2">For iPhone Users:</h3>
                                        <ol class="list-decimal pl-5 space-y-2 text-gray-600">
                                            <li>Go to <strong>Settings</strong> on your iPhone.</li>
                                            <li>Tap <strong>Cellular</strong> or <strong>Mobile Data</strong>.</li>
                                            <li>Tap <strong>Add Cellular Plan</strong> or <strong>Add Mobile Data Plan</strong>.</li>
                                            <li>Scan the QR code provided above or in your email.</li>
                                            <li>Follow the on-screen instructions to complete the installation.</li>
                                            <li>When prompted, select <strong>Add Cellular Plan</strong>.</li>
                                            <li>Choose a label for your new plan, such as "Travel" or the country name.</li>
                                            <li>Select your default line for calls, messages, and data.</li>
                                        </ol>
                                    </div>
                                    
                                    <div>
                                        <h3 class="text-base font-medium text-gray-900 mb-2">For Android Users:</h3>
                                        <ol class="list-decimal pl-5 space-y-2 text-gray-600">
                                            <li>Go to <strong>Settings</strong> on your Android device.</li>
                                            <li>Tap <strong>Network & Internet</strong> or <strong>Connections</strong>.</li>
                                            <li>Tap <strong>Mobile Network</strong> or <strong>SIM Manager</strong>.</li>
                                            <li>Tap <strong>Add Mobile Plan</strong> or <strong>+</strong> icon.</li>
                                            <li>Tap <strong>Download a New SIM Instead</strong> or <strong>Add Using QR Code</strong>.</li>
                                            <li>Scan the QR code provided above or in your email.</li>
                                            <li>Follow the on-screen instructions to complete the installation.</li>
                                            <li>When prompted, enter the activation code provided.</li>
                                        </ol>
                                    </div>
                                </div>
                            @endif
                            
                            <div class="mt-6 pt-6 border-t">
                                <h3 class="text-base font-medium text-gray-900 mb-4">Need Help?</h3>
                                <p class="text-gray-600 mb-4">
                                    If you're having trouble installing your eSIM, our support team is available 24/7 to assist you.
                                </p>
                                <div class="flex flex-col sm:flex-row gap-4">
                                    <a href="{{ route('help') }}" class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300 font-medium">
                                        <i class="fas fa-question-circle mr-2"></i> Visit Help Center
                                    </a>
                                    <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300 font-medium">
                                        <i class="fas fa-envelope mr-2"></i> Email Support
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                                
                <div>
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 sticky top-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                        
                        <div class="space-y-4 mb-6">
                            <div class="flex justify-between">
                                <span class="text-gray-600">{{ $order->esim->name }}</span>
                                <span class="font-medium">${{ number_format($order->amount, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tax</span>
                                <span class="font-medium">$0.00</span>
                            </div>
                        </div>
                        
<div class="border-t py-4 mb-6">
    <div class="flex justify-between items-center">
        <span class="text-gray-900 font-semibold">Total</span>
        <span class="text-xl font-bold text-black">${{ number_format($order->amount, 2) }}</span>
    </div>
</div>

<div class="space-y-4">
    <a href="{{ route('orders.index') }}" class="w-full inline-flex justify-center items-center px-6 py-3 rounded-xl transition-all duration-300 font-medium text-center block" style="background-color: #FEF952; color: black;">
        <i class="fas fa-arrow-left mr-2"></i> Back to Orders
    </a>
                            
                            <a href="mailto:<EMAIL>?subject=Order%20{{ $order->order_number }}%20Support" class="w-full block px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-300 font-medium text-center">
                                <i class="fas fa-headset mr-2"></i> Get Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
