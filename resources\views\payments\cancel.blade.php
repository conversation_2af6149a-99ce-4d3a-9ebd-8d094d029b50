@extends('layouts.app')

@section('title', 'Payment Cancelled - DataLendR')

@section('content')
<link rel="stylesheet" href="{{ asset('css/payment.css') }}">
<div class="container mx-auto px-4 py-12">
    <div class="max-w-3xl mx-auto bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
        <div class="bg-black p-6 border-b">
            <div class="flex items-center justify-center">
                <div class="bg-white/10 rounded-full p-3 mr-4">
                    <svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-white">Payment Cancelled</h1>
            </div>
        </div>
        
        <div class="p-6">
            <div class="mb-8 text-center">
                <p class="text-lg text-gray-600 mb-4">Your payment was cancelled or did not complete successfully.</p>
                <p class="text-gray-500">No charges have been made to your account.</p>
            </div>
            
            <div class="bg-gray-50 rounded-xl p-6 mb-6 border border-gray-100">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">What would you like to do next?</h2>
                
                <div class="space-y-4 text-gray-600">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <p>Try again with a different payment method</p>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                        <p>Check your payment details and try again</p>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p>Contact our support team if you need assistance</p>
                    </div>
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="{{ route('payments.checkout') }}" class="bg-yellow-500 hover:bg-yellow-400 text-black font-medium py-3 px-6 rounded-xl transition-all duration-300 text-center">
                    Try Again
                </a>
                <a href="{{ route('esims.cart') }}" class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-xl transition-all duration-300 text-center">
                    Return to Cart
                </a>
                <a href="{{ route('home') }}" class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-xl transition-all duration-300 text-center">
                    Back to Home
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
