@extends('layouts.app')

@section('title', 'Payment Successful - DataLendR')

@section('content')
<link rel="stylesheet" href="{{ asset('css/payment.css') }}">
<div class="container mx-auto px-4 py-12">
    <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <div class="bg-green-50 p-6 border-b border-green-100">
            <div class="flex items-center justify-center">
                <div class="bg-green-100 rounded-full p-3 mr-4">
                    <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">Payment Successful!</h1>
            </div>
        </div>
        
        <div class="p-6">
            <div class="mb-8 text-center">
                <p class="text-lg text-gray-600 mb-4">Thank you for your purchase. Your eSIM has been successfully ordered.</p>
                <p class="text-gray-500">Order #{{ $order->order_number }}</p>
            </div>
            
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Order Details</h2>
                
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="ml-4">
                            <h3 class="text-base font-medium text-gray-800">{{ $order->esim->name }}</h3>
                            <p class="text-sm text-gray-500">{{ $order->esim->data_amount }} - {{ $order->esim->validity_days }} days</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-base font-medium text-gray-800">${{ number_format($order->amount, 2) }}</p>
                    </div>
                </div>
                
                <div class="flex justify-between items-center py-3">
                    <p class="text-base text-gray-600">Total</p>
                    <p class="text-base font-semibold text-gray-800">${{ number_format($order->amount, 2) }}</p>
                </div>
            </div>
            
            <div class="bg-blue-50 rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">eSIM Activation</h2>
                
                @if($order->qr_code_url)
                    <div class="flex flex-col items-center mb-4">
                        <div class="bg-white p-3 rounded-lg shadow-sm mb-3">
                            <img src="{{ $order->qr_code_url }}" alt="eSIM QR Code" class="w-48 h-48">
                        </div>
                        <p class="text-sm text-gray-500">Scan this QR code with your device to activate your eSIM</p>
                    </div>
                @endif
                
                @if($order->activation_code)
                    <div class="bg-gray-100 rounded-md p-3 mb-4 text-center">
                        <p class="text-sm text-gray-500 mb-1">Activation Code</p>
                        <p class="text-lg font-mono font-semibold tracking-wider">{{ $order->activation_code }}</p>
                    </div>
                @endif
                
                <div class="text-sm text-gray-600">
                    <h3 class="font-semibold mb-2">Activation Instructions:</h3>
                    <p>{!! $order->instructions ?? 'To activate your eSIM, go to your device settings, select Mobile Data, Add Data Plan, and scan the QR code.' !!}</p>
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="{{ route('orders.show', $order->id) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition duration-300 text-center">
                    View Order Details
                </a>
                <a href="{{ route('home') }}" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-6 rounded-md transition duration-300 text-center">
                    Back to Home
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
