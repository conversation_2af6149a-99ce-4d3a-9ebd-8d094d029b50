<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\EsimController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Admin\CountryController as AdminCountryController;
use App\Http\Controllers\Admin\EsimController as AdminEsimController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\BlogController as AdminBlogController;
use App\Http\Controllers\Admin\HelpController as AdminHelpController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Home routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/features', [HomeController::class, 'features'])->name('features');
Route::get('/help', [HomeController::class, 'help'])->name('help');
Route::get('/blog', [HomeController::class, 'blog'])->name('blog');
Route::get('/blog/{slug}', [HomeController::class, 'blogPost'])->name('blog.post');
Route::get('/search', [HomeController::class, 'search'])->name('search');
Route::get('/privacy', [HomeController::class, 'privacy'])->name('privacy');
Route::get('/refund', [HomeController::class, 'refund'])->name('refund');
Route::get('/terms', [HomeController::class, 'terms'])->name('terms');

// Contact Us

Route::get('/contact', [ContactController::class, 'show'])->name('contact');
Route::post('/contact', [ContactController::class, 'submit'])->name('contact.submit');

// Country routes
Route::get('/countries', [CountryController::class, 'index'])->name('countries.index');
Route::get('/countries/sync', [CountryController::class, 'syncFromApi'])->name('countries.sync');
Route::get('/countries/{code}', [CountryController::class, 'show'])->name('countries.show');
// <AUTHOR>
Route::get('/packages', [CountryController::class, 'redirectToCountryByCode'])->name('packages.redirect');
// eSIM routes
Route::get('/esims/{id}', [EsimController::class, 'show'])->name('esims.show');
Route::get('/countries/{code}/esims/sync', [EsimController::class, 'syncFromApi'])->name('esims.sync');
Route::post('/esims/{id}/cart', [EsimController::class, 'addToCart'])->name('esims.addToCart');
Route::get('/cart', [EsimController::class, 'cart'])->name('cart');
Route::delete('/cart/{id}', [EsimController::class, 'removeFromCart'])->name('cart.remove');

// Coupon AJAX endpoints
Route::post('/cart/coupon/apply', [CouponController::class, 'apply'])->name('cart.coupon.apply');
Route::post('/cart/coupon/remove', [CouponController::class, 'remove'])->name('cart.coupon.remove');


Route::get('/checkout', [OrderController::class, 'checkout'])->name('checkout');
Route::post('/checkout', [OrderController::class, 'process'])->name('checkout.process');
Route::get('/confirmation/{orderNumber}', [OrderController::class, 'confirmation'])->name('orders.confirmation');


Route::get('/payments/checkout', [PaymentController::class, 'checkout'])->name('payments.checkout');
Route::post('/payments/process', [PaymentController::class, 'processPayment'])->name('payments.process');
Route::get('/payments/success', [PaymentController::class, 'success'])->name('payments.success');
Route::get('/payments/cancel', [PaymentController::class, 'cancel'])->name('payments.cancel');

// Stripe payment intent endpoint for AJAX
Route::post('/payments/intent', [\App\Http\Controllers\PaymentController::class, 'createPaymentIntent'])->name('payments.intent');

// Stripe Confirmation Token payment endpoint
Route::post('/payments/confirm', [App\Http\Controllers\PaymentController::class, 'processConfirmationToken'])->name('payments.confirm');

// Dashboard routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');
    Route::get('/dashboard/esims', [DashboardController::class, 'esims'])->name('dashboard.esims');
    Route::get('/dashboard/payment-methods', [DashboardController::class, 'paymentMethods'])->name('dashboard.payment-methods');
    Route::post('/dashboard/payment-methods/add', [DashboardController::class, 'addPaymentMethod'])->name('dashboard.payment-methods.add');
    Route::get('/dashboard/settings', [DashboardController::class, 'settings'])->name('dashboard.settings');
    Route::post('/dashboard/settings', [DashboardController::class, 'updateSettings'])->name('dashboard.settings.update');
    Route::get('/dashboard/orders', [OrderController::class, 'index'])->name('orders.index');
    Route::get('/dashboard/orders/{order}', [OrderController::class, 'show'])->name('orders.show');
});

// Stripe webhook
Route::post('/webhook/stripe', [WebhookController::class, 'handleStripeWebhook'])->name('webhook.stripe');

// Authentication routes
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
Route::get('/login', [LoginController::class, 'login'])->name('login');
Route::post('/login', [LoginController::class, 'process'])->name('login');

Route::get('/login/verify/{token}', [LoginController::class, 'verify'])->name('login.verify');

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', function () {
        return view('admin.dashboard');
    })->name('dashboard');
    Route::resource('countries', AdminCountryController::class);
    Route::resource('esims', AdminEsimController::class);
    Route::resource('users', AdminUserController::class);
    Route::resource('blog', AdminBlogController::class);
    Route::resource('help', AdminHelpController::class);
});