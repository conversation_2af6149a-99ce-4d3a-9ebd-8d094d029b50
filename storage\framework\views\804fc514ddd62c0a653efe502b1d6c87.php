<?php $__env->startSection('title', 'Checkout - DataLendR'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen py-10">
    <form id="payment-success-form" action="<?php echo e(route('payments.process')); ?>" method="POST">
    <?php echo csrf_field(); ?>
    <div class="max-w-6xl mx-auto px-4">
        <!-- Step Indicator -->
        <div class="flex items-center justify-center mb-8">
            <div class="flex items-center space-x-2 text-sm text-gray-600">
                <span class="flex items-center"><svg class="w-4 h-4 mr-2 text-black" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"></circle><text x="12" y="16" text-anchor="middle" font-size="10" fill="currentColor">1</text></svg>Cart</span>
                <span>›</span>
                <span class="flex items-center"><svg class="w-4 h-4 mr-2 text-black" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"></circle><text x="12" y="16" text-anchor="middle" font-size="10" fill="currentColor">2</text></svg>Billing</span>
                <span>›</span>
                <span class="flex items-center"><svg class="w-4 h-4 mr-2 text-black" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"></circle><text x="12" y="16" text-anchor="middle" font-size="10" fill="currentColor">3</text></svg>Payment</span>
            </div>
        </div>
        <h1 class="text-4xl font-extrabold text-black mb-8 flex items-center gap-2"><svg class="w-10 h-10 text-black mr-3 bg-yellow-500 p-2 rounded-full" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M2 10h20" stroke="currentColor" stroke-width="2"></path></svg> Checkout</h1>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Order Summary -->
            <div class="md:col-span-2">
                <div class="bg-white border border-gray-100 rounded-xl shadow-lg p-8 mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2"><svg class="w-6 h-6 text-black" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4z" stroke="currentColor" stroke-width="2" fill="none"></path><path d="M2 20c0-4 8-6 10-6s10 2 10 6" stroke="currentColor" stroke-width="2" fill="none"></path></svg> Billing Information</h2>
                    <div class="grid grid-cols-1 gap-6 mb-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                            <input type="text" id="name" name="name" value="<?php echo e(auth()->user() ? auth()->user()->name : ''); ?>" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400" required>
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" id="email" name="email" value="<?php echo e(auth()->user() ? auth()->user()->email : ''); ?>" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400" required>
                            <p class="text-xs text-gray-500 mt-2">Your eSIM is instantly sent to your email. You can also request a login link anytime to check your data usage or view your order details if needed.</p>
                            <p id="email-errors" class="mt-3 text-red-500 text-sm mb-4 border border-red-500 rounded-md p-5 hidden"></p>
                        </div>
                    </div>
                </div>                
                <div class="bg-white border border-gray-100 rounded-xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2"><svg class="w-6 h-6 text-black" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M2 10h20" stroke="currentColor" stroke-width="2"></path></svg> Payment Method</h2>
                    <div id="payment-form">
                        <div id="payment-element" class="mb-6" data-stripe-key="<?php echo e(config('services.stripe.key')); ?>">
                            <!-- Stripe Elements will be inserted here -->
                        </div>
                        <div id="card-errors" class="text-red-500 text-sm mb-4"></div>
                        <p class="text-xs text-gray-500 mt-2">Payment are processed securely and are encrypted. We do not store your card details.</p>                           
                    </div>
                </div> 
            </div>
            <!-- Payment Form -->
            <div class="md:col-span-1 sticky top-6">
                <div class="bg-white border border-gray-100 rounded-xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2"><svg class="w-6 h-6 text-black" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M2 10h20" stroke="currentColor" stroke-width="2"></path></svg> Order Summary</h2>
                    <?php if(count($cartItems) > 0): ?>
                        <div class="border-b border-gray-200 pb-4 mb-4">
                            <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $esim): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex justify-between items-center py-3 group">
                                    <div class="flex items-center">
                                        <div class="">
                                            <h3 class="text-base font-semibold text-gray-800 group-hover:text-black"><?php echo e($esim['country']); ?></h3>
                                            <p class="text-xs text-gray-500"><?php echo e($esim['name']); ?></p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-base font-bold text-gray-900 group-hover:text-black">$<?php echo e(number_format($esim['price'], 2)); ?></p>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <p class="text-base text-gray-600">Subtotal</p>
                            <p class="text-base font-medium text-gray-800">$<?php echo e(number_format($total, 2)); ?></p>
                        </div>
                        <?php if(isset($discount) && $discount > 0): ?>
                        <div id="coupon-field" class="flex justify-between items-center text-green-700">
                            <span class="font-medium">Discount (<?php echo e($coupon['code'] ?? ''); ?>)</span>
                            <span class="font-medium">- $<?php echo e(number_format($discount, 2)); ?></span>
                        </div>
                        <?php else: ?>
                        <div id="coupon-field" class="flex justify-between items-center text-green-700 hidde">
                            <span class="font-medium"></span>
                            <span class="font-medium"></span>
                        </div>
                        <?php endif; ?>
                        <div class="flex justify-between items-center py-2">
                            <p class="text-base text-gray-600">Tax</p>
                            <p class="text-base font-medium text-gray-800">$<?php echo e(number_format($tax, 2)); ?></p>
                        </div>
                        <div class="border-t mt-2 pt-3">
                            <label for="discount_code" class="block text-sm font-medium text-gray-700 mb-2">Discount Code</label>
                            <div class="flex relative">
                                <input type="text" id="discount_code" name="discount_code" placeholder="Enter code" class="input-field rounded-lg border border-gray-100 pl-2 w-full py-3" value="<?php echo e(session('coupon.code') ?? ''); ?>">
                                <button id="apply-coupon-btn" type="button" class="bg-yellow-500 text-black px-4 py-2 rounded-lg hover:bg-yellow-400 transition-colors duration-300 text-sm absolute right-2 top-2 <?php echo e(session('coupon') ? 'hidden' : ''); ?>">
                                    Apply
                                </button>
                                <button id="remove-coupon-btn" type="button" class="text-sm ml-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors duration-300 <?php echo e(session('coupon') ? '' : 'hidden'); ?> absolute right-2 top-2">
                                    X
                                </button>
                            </div>
                            <p id="coupon-message" class="text-green-600 text-sm mt-1 <?php echo e(session('coupon') ? '' : 'hidden'); ?>">
                                <i class="fas fa-check-circle mr-1"></i> <span id="coupon-success-msg"><?php echo e(session('coupon.discount_percent') ? session('coupon.discount_percent').'% discount applied!' : ''); ?></span>
                            </p>
                            <p id="coupon-error" class="text-red-600 text-sm mt-1 hidden"></p>
                        </div>                        
                        <div class="flex justify-between items-center py-2 border-t mt-4">
                            <span class="text-gray-900 font-semibold">Total:</span>
                            <span class="inline-flex items-center text-black text-lg font-bold">$<?php echo e(number_format($grandTotal, 2)); ?></span>
                        </div>
                    <?php else: ?>
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700 p-4 rounded mb-4">
                            <p>Your cart is empty.</p>
                        </div>
                    <?php endif; ?>
                    <button id="submit-button" class="mt-5 w-full bg-black text-white hover:from-yellow-600 hover:to-yellow-500 font-bold py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center gap-2 shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-400">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M2 10h20" stroke="currentColor" stroke-width="2"></path></svg>
                        <span id="button-text">Pay $<?php echo e(number_format($grandTotal, 2)); ?></span>
                        <div id="spinner" class="hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </div>
                    </button>
                    <div class="mt-6 text-center text-sm text-gray-600">
                        <p class="flex items-center justify-center">
                            <i class="fas fa-lock mr-2 text-gray-500"></i> Secure checkout
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="payment_intent_id" name="payment_intent_id">
    </form>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script src="https://js.stripe.com/v3/"></script>
<script src="<?php echo e(asset('js/stripe-checkout.js')); ?>?v=2.0"></script>
<script>
    // Set the Stripe key for the payment element
    document.addEventListener('DOMContentLoaded', function() {
        const paymentElement = document.getElementById('payment-element');
        if (paymentElement) {
            paymentElement.dataset.stripeKey = '<?php echo e(config('services.stripe.key')); ?>';
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        const applyBtn = document.getElementById('apply-coupon-btn');
        const removeBtn = document.getElementById('remove-coupon-btn');
        const codeInput = document.getElementById('discount_code');
        const msg = document.getElementById('coupon-message');
        const errorMsg = document.getElementById('coupon-error');
        const successMsg = document.getElementById('coupon-success-msg');
        const couponField = document.getElementById('coupon-field');
        const totalSpan = document.querySelector('.inline-flex.items-center.text-black.text-lg.font-bold');

        // Add loading spinner to coupon buttons
        function setCouponButtonLoading(btn, loading) {
            if (!btn) return;
            if (loading) {
                btn.disabled = true;
                btn.dataset.originalText = btn.innerHTML;
                btn.innerHTML = '<span class="spinner-border spinner-border-sm mr-2"></span>Processing...';
            } else {
                btn.disabled = false;
                if (btn.dataset.originalText) btn.innerHTML = btn.dataset.originalText;
            }
        }

        // Helper to update coupon field and total
        function updateCouponAndTotal() {
            fetch('/payments/intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                }
            })
            .then(res => res.json())
            .then(data => {
                // Coupon info
                if (data && data.amount !== undefined) {
                    if (data.discount && data.coupon) {
                        couponField.classList.remove('hidden');
                        couponField.querySelector('span:nth-child(1)').textContent = `Discount (${data.coupon})`;
                        couponField.querySelector('span:nth-child(2)').textContent = `- $${parseFloat(data.discount).toFixed(2)}`;
                    } else {
                        couponField.classList.add('hidden');
                        couponField.querySelector('span:nth-child(1)').textContent = '';
                        couponField.querySelector('span:nth-child(2)').textContent = '';
                    }
                    // Update total
                    if (totalSpan) {
                        totalSpan.textContent = `$${parseFloat(data.amount).toFixed(2)}`;
                    }
                }
            });
        }

        applyBtn.addEventListener('click', function() {
            setCouponButtonLoading(applyBtn, true);
            fetch("<?php echo e(route('cart.coupon.apply')); ?>", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({ code: codeInput.value })
            })
            .then(res => res.json())
            .then(data => {
                setCouponButtonLoading(applyBtn, false);
                if(data.success) {
                    msg.classList.remove('hidden');
                    errorMsg.classList.add('hidden');
                    successMsg.textContent = data.discount_percent + '% discount applied!';
                    removeBtn.classList.remove('hidden');
                    applyBtn.classList.add('hidden');
                    updateCouponAndTotal();
                    document.dispatchEvent(new CustomEvent('couponChanged'));
                } else {
                    msg.classList.add('hidden');
                    errorMsg.textContent = data.message;
                    errorMsg.classList.remove('hidden');
                }
            })
            .catch(() => setCouponButtonLoading(applyBtn, false));
        });
        if(removeBtn) {
            removeBtn.addEventListener('click', function() {
                setCouponButtonLoading(removeBtn, true);
                fetch("<?php echo e(route('cart.coupon.remove')); ?>", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                })
                .then(res => res.json())
                .then(data => {
                    setCouponButtonLoading(removeBtn, false);
                    if(data.success) {
                        msg.classList.add('hidden');
                        errorMsg.classList.add('hidden');
                        codeInput.value = '';
                        removeBtn.classList.add('hidden');
                        applyBtn.classList.remove('hidden');
                        updateCouponAndTotal();
                        document.dispatchEvent(new CustomEvent('couponChanged'));
                    }
                })
                .catch(() => setCouponButtonLoading(removeBtn, false));
            });
        }
    });

    document.addEventListener('couponChanged', function() {
        if (window.refreshStripePaymentForm) {
            window.refreshStripePaymentForm();
        } else {
            location.reload(); // fallback if JS not loaded
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\wamp64\www\projects\datalendr\resources\views/payments/checkout.blade.php ENDPATH**/ ?>