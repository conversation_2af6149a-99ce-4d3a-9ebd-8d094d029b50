/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#2563eb', // Blue-600
          light: '#60a5fa',   // Blue-400
          dark: '#1e40af',    // Blue-800
          gradient1: '#2563eb',
          gradient2: '#38bdf8',
        },
        secondary: {
          DEFAULT: '#f59e42', // Orange-400
          dark: '#b45309',
        },
        neutral: {
          light: '#f3f4f6',
          DEFAULT: '#e5e7eb',
          dark: '#374151',
        },
        yellow: '#fef952',
        accent: '#16a34a', // Green-600
        danger: '#ef4444',
        warning: '#facc15',
        info: '#38bdf8',
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
        heading: ['Poppins', 'ui-sans-serif'],
      },
      boxShadow: {
        card: '0 4px 24px 0 rgba(37,99,235,0.08)',
        glass: '0 8px 32px 0 rgba(31,41,55,0.18)',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'pattern-dots': "url('/images/pattern-dots.svg')",
      },
      borderRadius: {
        xl: '1.25rem',
      },
      transitionProperty: {
        'height': 'height',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
